const mongoose = require('mongoose');
const MembershipConfig = require('./models/MembershipConfig');

// 连接数据库
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB连接成功');
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    return false;
  }
};

// 初始化会员配置
const initMembershipConfigs = async () => {
  try {
    // 检查是否已存在配置
    const existingConfigs = await MembershipConfig.find();
    if (existingConfigs.length > 0) {
      console.log('✅ 会员配置已存在，跳过初始化');
      return;
    }

    // 创建初始会员配置
    const initialConfigs = [
      {
        level: 'monthly',
        name: '月会员',
        price: 316.88,
        originalPrice: 516.88,
        duration: 30,
        features: {
          strategies: {
            contracts: 6,
            spot: 3
          },
          service: {
            support: '工作日客服（30分钟响应）',
            response: '基础教程包'
          },
          benefits: {
            discount: '首单9折',
            trial: '月1次策略体验（3天）',
            referral: ''
          }
        },
        order: 1
      },
      {
        level: 'quarterly',
        name: '季会员',
        price: 466.88,
        originalPrice: 766.88,
        duration: 90,
        features: {
          strategies: {
            contracts: 8,
            spot: 4
          },
          service: {
            support: '专属客服（15分钟响应）',
            response: '进阶教程包'
          },
          benefits: {
            discount: '8.5折优惠',
            trial: '季2次策略体验（5天）',
            referral: '推荐1人得50元消费券'
          }
        },
        order: 2
      },
      {
        level: 'yearly',
        name: '年会员',
        price: 1060.88,
        originalPrice: 1688.88,
        duration: 365,
        features: {
          strategies: {
            contracts: 10,
            spot: 5
          },
          service: {
            support: '7×24小时极速客服',
            response: '每月策略培训'
          },
          benefits: {
            discount: '8折优惠',
            trial: '年4次新策略优先体验（7天）',
            referral: '推荐1人得100元消费券'
          }
        },
        order: 3
      },
      {
        level: 'lifetime',
        name: '永久会员',
        price: 3999.99,
        originalPrice: 9999.99,
        duration: -1, // -1表示永久
        features: {
          strategies: {
            contracts: 999,
            spot: 999
          },
          service: {
            support: '7×24专属客户经理',
            response: '季度策略研讨会'
          },
          benefits: {
            discount: '终身版本免费升级',
            trial: '推荐无上限（200元/人）',
            referral: '收益分成权益'
          }
        },
        order: 4
      }
    ];

    // 批量插入配置
    await MembershipConfig.insertMany(initialConfigs);
    console.log('✅ 会员配置初始化成功');

    // 显示创建的配置
    console.log('\n📋 已创建的会员配置:');
    const configs = await MembershipConfig.find().sort({ order: 1 });
    configs.forEach(config => {
      console.log(`- ${config.name}: ¥${config.price} (原价: ¥${config.originalPrice})`);
    });

  } catch (error) {
    console.error('❌ 初始化会员配置失败:', error);
  }
};

// 主函数
const main = async () => {
  console.log('🚀 开始初始化会员配置...');

  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.error('❌ 数据库连接失败，无法初始化会员配置');
    process.exit(1);
  }

  await initMembershipConfigs();

  console.log('✅ 会员配置初始化完成');
  process.exit(0);
};

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { initMembershipConfigs };
