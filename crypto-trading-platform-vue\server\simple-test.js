const axios = require('axios');

async function testLogin() {
  try {
    console.log('测试管理员登录...');
    const response = await axios.post('http://localhost:3009/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });
    
    console.log('登录响应:', response.data);
    
    if (response.data.success) {
      const token = response.data.token;
      console.log('登录成功，获取到token');
      
      // 测试获取用户列表
      console.log('\n测试获取用户列表...');
      const usersResponse = await axios.get('http://localhost:3009/api/user/admin/users', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });
      
      console.log('用户列表响应:', usersResponse.data);
    }
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

testLogin();
