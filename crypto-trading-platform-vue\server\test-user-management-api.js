const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3009/api';

// 测试用户管理API
const testUserManagementAPI = async () => {
  try {
    console.log('=== 测试用户管理API ===\n');

    // 1. 首先登录获取管理员token
    console.log('1. 管理员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 管理员登录失败:', loginResponse.data.error);
      return;
    }

    const token = loginResponse.data.token;
    const adminUser = loginResponse.data.user;
    console.log(`✅ 管理员登录成功: ${adminUser.username} (${adminUser.email})`);
    console.log(`管理员权限: ${adminUser.isAdmin}`);

    // 设置请求头
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 测试获取用户列表
    console.log('\n2. 获取用户列表...');
    const usersResponse = await axios.get(`${BASE_URL}/user/admin/users`, { headers });
    
    if (usersResponse.data.success) {
      const users = usersResponse.data.data.users;
      const pagination = usersResponse.data.data.pagination;
      
      console.log(`✅ 获取用户列表成功 (${users.length} 个用户):`);
      users.forEach(user => {
        console.log(`  - ${user.username} (${user.email}) - 会员: ${user.membershipLevel} - 管理员: ${user.isAdmin}`);
      });
      console.log(`分页信息: 第 ${pagination.page} 页，共 ${pagination.pages} 页，总计 ${pagination.total} 个用户`);
    } else {
      console.error('❌ 获取用户列表失败:', usersResponse.data.error);
      return;
    }

    // 3. 测试搜索用户
    console.log('\n3. 测试搜索用户...');
    const searchResponse = await axios.get(`${BASE_URL}/user/admin/users?search=admin`, { headers });
    
    if (searchResponse.data.success) {
      const searchUsers = searchResponse.data.data.users;
      console.log(`✅ 搜索用户成功 (${searchUsers.length} 个结果):`);
      searchUsers.forEach(user => {
        console.log(`  - ${user.username} (${user.email})`);
      });
    } else {
      console.error('❌ 搜索用户失败:', searchResponse.data.error);
    }

    // 4. 测试获取用户详细信息
    const testUser = usersResponse.data.data.users.find(u => u.email !== '<EMAIL>');
    if (testUser) {
      console.log(`\n4. 获取用户详细信息 - ${testUser.username}...`);
      const userDetailResponse = await axios.get(`${BASE_URL}/user/admin/users/${testUser.id}`, { headers });
      
      if (userDetailResponse.data.success) {
        const userDetail = userDetailResponse.data.data;
        console.log(`✅ 获取用户详细信息成功:`);
        console.log(`  - 用户名: ${userDetail.username}`);
        console.log(`  - 邮箱: ${userDetail.email}`);
        console.log(`  - UID: ${userDetail.uid}`);
        console.log(`  - 会员等级: ${userDetail.membershipLevel}`);
        console.log(`  - 会员剩余天数: ${userDetail.membershipDaysLeft}`);
        console.log(`  - 是否激活: ${userDetail.isActive}`);
        console.log(`  - 是否管理员: ${userDetail.isAdmin}`);
      } else {
        console.error('❌ 获取用户详细信息失败:', userDetailResponse.data.error);
      }

      // 5. 测试更新用户会员等级
      console.log(`\n5. 测试更新用户会员等级 - ${testUser.username}...`);
      const membershipUpdateResponse = await axios.put(
        `${BASE_URL}/user/admin/users/${testUser.id}/membership`,
        {
          membershipLevel: 'quarterly',
          duration: 90
        },
        { headers }
      );
      
      if (membershipUpdateResponse.data.success) {
        const updatedData = membershipUpdateResponse.data.data;
        console.log(`✅ 更新会员等级成功:`);
        console.log(`  - 新会员等级: ${updatedData.membershipLevel}`);
        console.log(`  - 到期时间: ${updatedData.membershipExpiry}`);
        console.log(`  - 剩余天数: ${updatedData.membershipDaysLeft}`);
      } else {
        console.error('❌ 更新会员等级失败:', membershipUpdateResponse.data.error);
      }

      // 6. 测试更新用户状态
      console.log(`\n6. 测试更新用户状态 - ${testUser.username}...`);
      const statusUpdateResponse = await axios.put(
        `${BASE_URL}/user/admin/users/${testUser.id}/status`,
        {
          isActive: true,
          isAdmin: false
        },
        { headers }
      );
      
      if (statusUpdateResponse.data.success) {
        const updatedStatus = statusUpdateResponse.data.data;
        console.log(`✅ 更新用户状态成功:`);
        console.log(`  - 激活状态: ${updatedStatus.isActive}`);
        console.log(`  - 管理员权限: ${updatedStatus.isAdmin}`);
      } else {
        console.error('❌ 更新用户状态失败:', statusUpdateResponse.data.error);
      }
    }

    // 7. 测试非管理员访问（应该失败）
    console.log('\n7. 测试非管理员访问权限...');
    try {
      // 使用无效token
      const invalidHeaders = {
        'Authorization': 'Bearer invalid_token',
        'Content-Type': 'application/json'
      };
      
      await axios.get(`${BASE_URL}/user/admin/users`, { headers: invalidHeaders });
      console.error('❌ 权限验证失败：应该拒绝无效token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 权限验证正常：正确拒绝了无效token');
      } else {
        console.error('❌ 权限验证异常:', error.message);
      }
    }

    console.log('\n✅ 用户管理API测试完成');

  } catch (error) {
    console.error('❌ API测试过程中出错:', error.response?.data || error.message);
  }
};

// 运行测试
testUserManagementAPI().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
