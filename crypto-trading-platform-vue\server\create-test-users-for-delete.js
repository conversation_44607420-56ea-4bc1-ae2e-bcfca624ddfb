const mongoose = require('mongoose');
const User = require('./models/User');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/crypto-trading-platform');
    console.log('✅ MongoDB连接成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }
};

// 创建测试用户
const createTestUsers = async () => {
  try {
    console.log('=== 创建测试用户用于删除功能测试 ===\n');

    // 创建几个测试用户
    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'test123',
        username: 'testuser1',
        membershipLevel: 'monthly'
      },
      {
        email: '<EMAIL>',
        password: 'test123',
        username: 'testuser2',
        membershipLevel: 'quarterly'
      },
      {
        email: '<EMAIL>',
        password: 'test123',
        username: 'testuser3',
        membershipLevel: 'none'
      },
      {
        email: '<EMAIL>',
        password: 'test123',
        username: 'deletetest',
        membershipLevel: 'yearly'
      }
    ];

    for (const userData of testUsers) {
      try {
        // 检查用户是否已存在
        const existingUser = await User.findOne({ email: userData.email });
        if (existingUser) {
          console.log(`用户 ${userData.email} 已存在，跳过创建`);
          continue;
        }

        // 创建新用户
        const user = new User({
          uid: User.generateUID(),
          email: userData.email,
          password: userData.password,
          username: userData.username,
          membershipLevel: userData.membershipLevel,
          isActive: true,
          isAdmin: false
        });

        // 如果是会员，设置会员信息
        if (userData.membershipLevel !== 'none') {
          user.membershipStartDate = new Date();
          const expiryDate = new Date();
          
          switch (userData.membershipLevel) {
            case 'monthly':
              expiryDate.setDate(expiryDate.getDate() + 30);
              break;
            case 'quarterly':
              expiryDate.setDate(expiryDate.getDate() + 90);
              break;
            case 'yearly':
              expiryDate.setDate(expiryDate.getDate() + 365);
              break;
          }
          
          user.membershipExpiry = expiryDate;
        }

        await user.save();
        console.log(`✅ 创建用户成功: ${user.username} (${user.email}) - 会员等级: ${user.membershipLevel}`);
      } catch (error) {
        console.error(`❌ 创建用户 ${userData.email} 失败:`, error.message);
      }
    }

    // 显示所有用户
    console.log('\n=== 当前所有用户 ===');
    const allUsers = await User.find({}).select('-password');
    allUsers.forEach(user => {
      console.log(`- ${user.username} (${user.email}) - 管理员: ${user.isAdmin} - 会员: ${user.membershipLevel} - 激活: ${user.isActive}`);
    });

    console.log(`\n总用户数: ${allUsers.length}`);
    console.log('\n现在可以测试删除功能了！');

  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await createTestUsers();
  
  console.log('\n=== 操作完成 ===');
  process.exit(0);
};

// 运行
main().catch(error => {
  console.error('❌ 操作失败:', error);
  process.exit(1);
});
