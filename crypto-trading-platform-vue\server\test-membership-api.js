const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3009/api';

// 测试会员信息API
const testMembershipAPI = async () => {
  try {
    console.log('=== 测试会员信息API ===\n');

    // 1. 首先登录获取token
    console.log('1. 用户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 用户登录失败:', loginResponse.data.error);
      return;
    }

    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    console.log(`✅ 用户登录成功: ${user.username}`);
    console.log(`登录时的会员信息:`);
    console.log(`  - 会员等级: ${user.membershipLevel}`);
    console.log(`  - 剩余天数: ${user.membershipDaysLeft === -1 ? '永久' : user.membershipDaysLeft}`);
    console.log(`  - 是否有效会员: ${user.isValidMember}`);

    // 2. 调用会员信息API
    console.log('\n2. 获取最新会员信息...');
    const membershipResponse = await axios.get(`${BASE_URL}/membership/info`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (membershipResponse.data.success) {
      const membershipInfo = membershipResponse.data.data;
      console.log('✅ 获取会员信息成功:');
      console.log(`  - 会员等级: ${membershipInfo.membershipLevel}`);
      console.log(`  - 剩余天数: ${membershipInfo.membershipDaysLeft === -1 ? '永久' : membershipInfo.membershipDaysLeft}`);
      console.log(`  - 是否有效会员: ${membershipInfo.isValidMember}`);
      console.log(`  - 会员开始时间: ${membershipInfo.membershipStartDate}`);
      console.log(`  - 会员到期时间: ${membershipInfo.membershipExpiry}`);
    } else {
      console.error('❌ 获取会员信息失败:', membershipResponse.data.error);
    }

    // 3. 测试管理员用户
    console.log('\n3. 测试管理员用户...');
    const adminLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (adminLoginResponse.data.success) {
      const adminToken = adminLoginResponse.data.token;
      const adminUser = adminLoginResponse.data.user;
      console.log(`✅ 管理员登录成功: ${adminUser.username}`);
      
      const adminMembershipResponse = await axios.get(`${BASE_URL}/membership/info`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });

      if (adminMembershipResponse.data.success) {
        const adminMembershipInfo = adminMembershipResponse.data.data;
        console.log('✅ 管理员会员信息:');
        console.log(`  - 会员等级: ${adminMembershipInfo.membershipLevel}`);
        console.log(`  - 剩余天数: ${adminMembershipInfo.membershipDaysLeft === -1 ? '永久' : adminMembershipInfo.membershipDaysLeft}`);
        console.log(`  - 是否有效会员: ${adminMembershipInfo.isValidMember}`);
      }
    }

    // 4. 测试永久会员用户
    console.log('\n4. 测试永久会员用户...');
    const lifetimeLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (lifetimeLoginResponse.data.success) {
      const lifetimeToken = lifetimeLoginResponse.data.token;
      const lifetimeUser = lifetimeLoginResponse.data.user;
      console.log(`✅ 永久会员登录成功: ${lifetimeUser.username}`);
      
      const lifetimeMembershipResponse = await axios.get(`${BASE_URL}/membership/info`, {
        headers: { Authorization: `Bearer ${lifetimeToken}` }
      });

      if (lifetimeMembershipResponse.data.success) {
        const lifetimeMembershipInfo = lifetimeMembershipResponse.data.data;
        console.log('✅ 永久会员信息:');
        console.log(`  - 会员等级: ${lifetimeMembershipInfo.membershipLevel}`);
        console.log(`  - 剩余天数: ${lifetimeMembershipInfo.membershipDaysLeft === -1 ? '永久' : lifetimeMembershipInfo.membershipDaysLeft}`);
        console.log(`  - 是否有效会员: ${lifetimeMembershipInfo.isValidMember}`);
      }
    }

    // 5. 测试过期会员用户
    console.log('\n5. 测试过期会员用户...');
    const expiredLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (expiredLoginResponse.data.success) {
      const expiredToken = expiredLoginResponse.data.token;
      const expiredUser = expiredLoginResponse.data.user;
      console.log(`✅ 过期会员登录成功: ${expiredUser.username}`);
      
      const expiredMembershipResponse = await axios.get(`${BASE_URL}/membership/info`, {
        headers: { Authorization: `Bearer ${expiredToken}` }
      });

      if (expiredMembershipResponse.data.success) {
        const expiredMembershipInfo = expiredMembershipResponse.data.data;
        console.log('✅ 过期会员信息:');
        console.log(`  - 会员等级: ${expiredMembershipInfo.membershipLevel}`);
        console.log(`  - 剩余天数: ${expiredMembershipInfo.membershipDaysLeft === -1 ? '永久' : expiredMembershipInfo.membershipDaysLeft}`);
        console.log(`  - 是否有效会员: ${expiredMembershipInfo.isValidMember}`);
      }
    }

    console.log('\n✅ 会员信息API测试完成');

  } catch (error) {
    console.error('❌ API测试过程中出错:', error.response?.data || error.message);
  }
};

// 运行测试
testMembershipAPI().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
