const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { authenticateToken } = require('./auth');

// 验证管理员权限中间件
const requireAdmin = (req, res, next) => {
  if (!req.user.isAdmin) {
    return res.status(403).json({ success: false, error: '需要管理员权限' });
  }
  next();
};

// 获取所有用户列表（管理员专用）
router.get('/admin/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '' } = req.query;
    const skip = (page - 1) * limit;

    // 构建搜索条件
    let searchCondition = {};
    if (search) {
      searchCondition = {
        $or: [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { uid: { $regex: search, $options: 'i' } }
        ]
      };
    }

    // 获取用户列表
    const users = await User.find(searchCondition)
      .select('-password') // 不返回密码字段
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // 获取总数
    const total = await User.countDocuments(searchCondition);

    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      id: user._id,
      uid: user.uid,
      email: user.email,
      username: user.username,
      avatar: user.avatar,
      isActive: user.isActive,
      isAdmin: user.isAdmin,
      membershipLevel: user.membershipLevel,
      membershipExpiry: user.membershipExpiry,
      membershipStartDate: user.membershipStartDate,
      isValidMember: user.isValidMember(),
      membershipDaysLeft: user.getMembershipDaysLeft(),
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt
    }));

    res.json({
      success: true,
      data: {
        users: formattedUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      error: '获取用户列表失败'
    });
  }
});

// 更新用户会员等级（管理员专用）
router.put('/admin/users/:userId/membership', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { membershipLevel, duration } = req.body;

    // 验证会员等级
    const validLevels = ['none', 'monthly', 'quarterly', 'yearly', 'lifetime'];
    if (!validLevels.includes(membershipLevel)) {
      return res.status(400).json({
        success: false,
        error: '无效的会员等级'
      });
    }

    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const now = new Date();

    // 更新会员信息
    user.membershipLevel = membershipLevel;
    user.membershipStartDate = membershipLevel === 'none' ? null : now;

    // 计算到期时间
    if (membershipLevel === 'none') {
      user.membershipExpiry = null;
    } else if (membershipLevel === 'lifetime') {
      user.membershipExpiry = null; // 永久会员没有到期时间
    } else {
      // 使用传入的天数或默认天数
      let days = duration;
      if (!days) {
        switch (membershipLevel) {
          case 'monthly':
            days = 30;
            break;
          case 'quarterly':
            days = 90;
            break;
          case 'yearly':
            days = 365;
            break;
          default:
            days = 30;
        }
      }

      const expiryDate = new Date(now);
      expiryDate.setDate(expiryDate.getDate() + days);
      user.membershipExpiry = expiryDate;
    }

    await user.save();

    res.json({
      success: true,
      message: '用户会员等级更新成功',
      data: {
        uid: user.uid,
        username: user.username,
        membershipLevel: user.membershipLevel,
        membershipExpiry: user.membershipExpiry,
        membershipStartDate: user.membershipStartDate,
        isValidMember: user.isValidMember(),
        membershipDaysLeft: user.getMembershipDaysLeft()
      }
    });
  } catch (error) {
    console.error('更新用户会员等级错误:', error);
    res.status(500).json({
      success: false,
      error: '更新用户会员等级失败'
    });
  }
});

// 更新用户状态（管理员专用）
router.put('/admin/users/:userId/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive, isAdmin } = req.body;

    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    // 防止管理员取消自己的管理员权限
    if (req.user._id.toString() === userId && isAdmin === false) {
      return res.status(400).json({
        success: false,
        error: '不能取消自己的管理员权限'
      });
    }

    // 更新用户状态
    if (typeof isActive === 'boolean') {
      user.isActive = isActive;
    }
    if (typeof isAdmin === 'boolean') {
      user.isAdmin = isAdmin;
    }

    await user.save();

    res.json({
      success: true,
      message: '用户状态更新成功',
      data: {
        uid: user.uid,
        username: user.username,
        isActive: user.isActive,
        isAdmin: user.isAdmin
      }
    });
  } catch (error) {
    console.error('更新用户状态错误:', error);
    res.status(500).json({
      success: false,
      error: '更新用户状态失败'
    });
  }
});

// 获取用户详细信息（管理员专用）
router.get('/admin/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId).select('-password');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: user._id,
        uid: user.uid,
        email: user.email,
        username: user.username,
        avatar: user.avatar,
        isActive: user.isActive,
        isAdmin: user.isAdmin,
        membershipLevel: user.membershipLevel,
        membershipExpiry: user.membershipExpiry,
        membershipStartDate: user.membershipStartDate,
        isValidMember: user.isValidMember(),
        membershipDaysLeft: user.getMembershipDaysLeft(),
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error('获取用户详细信息错误:', error);
    res.status(500).json({
      success: false,
      error: '获取用户详细信息失败'
    });
  }
});

// 删除用户（管理员专用）
router.delete('/admin/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    // 查找要删除的用户
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    // 安全检查：不能删除管理员用户
    if (user.isAdmin) {
      return res.status(400).json({
        success: false,
        error: '不能删除管理员用户'
      });
    }

    // 安全检查：不能删除自己
    if (req.user._id.toString() === userId) {
      return res.status(400).json({
        success: false,
        error: '不能删除自己的账户'
      });
    }

    // 记录删除的用户信息（用于日志）
    const deletedUserInfo = {
      uid: user.uid,
      username: user.username,
      email: user.email,
      membershipLevel: user.membershipLevel,
      createdAt: user.createdAt
    };

    // 删除用户
    await User.findByIdAndDelete(userId);

    console.log(`管理员 ${req.user.username} 删除了用户:`, deletedUserInfo);

    res.json({
      success: true,
      message: '用户删除成功',
      data: {
        deletedUser: {
          uid: deletedUserInfo.uid,
          username: deletedUserInfo.username,
          email: deletedUserInfo.email
        }
      }
    });
  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({
      success: false,
      error: '删除用户失败'
    });
  }
});

module.exports = router;
