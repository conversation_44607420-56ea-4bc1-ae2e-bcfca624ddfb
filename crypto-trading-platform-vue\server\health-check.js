const axios = require('axios');

async function healthCheck() {
  try {
    console.log('检查服务器健康状态...');
    const response = await axios.get('http://localhost:3009/api/health', {
      timeout: 5000
    });
    
    console.log('健康检查响应:', response.data);
    console.log('服务器状态: 正常');
    
    // 测试登录
    console.log('\n测试登录API...');
    const loginResponse = await axios.post('http://localhost:3009/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    }, {
      timeout: 10000
    });
    
    console.log('登录响应:', loginResponse.data);
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('❌ 无法连接到服务器');
    } else if (error.code === 'ECONNABORTED') {
      console.error('❌ 请求超时');
    } else {
      console.error('❌ 错误:', error.response?.data || error.message);
    }
  }
}

healthCheck();
