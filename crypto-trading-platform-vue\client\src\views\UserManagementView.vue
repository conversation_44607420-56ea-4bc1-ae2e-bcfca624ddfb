<template>
  <div class="user-management-view">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <button class="back-button" @click="goBack">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1>用户管理</h1>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 搜索和筛选 -->
      <div class="search-section">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索用户名、邮箱或UID..."
            @input="handleSearch"
          />
        </div>
        <button class="refresh-button" @click="loadUsers" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
        </button>
      </div>

      <!-- 用户列表 -->
      <div class="users-section">
        <div v-if="loading && users.length === 0" class="loading-section">
          <i class="fas fa-spinner fa-spin"></i>
          <p>正在加载用户列表...</p>
        </div>

        <div v-else-if="users.length === 0" class="empty-section">
          <i class="fas fa-users"></i>
          <p>暂无用户数据</p>
        </div>

        <div v-else class="users-list">
          <div
            class="user-card"
            v-for="user in users"
            :key="user.id"
            @click="selectUser(user)"
          >
            <div class="user-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="user-info">
              <div class="user-name">{{ user.username }}</div>
              <div class="user-email">{{ user.email }}</div>
              <div class="user-uid">UID: {{ user.uid }}</div>
            </div>
            <div class="user-status">
              <div class="membership-level" :class="getMembershipClass(user.membershipLevel)">
                {{ getMembershipLevelName(user.membershipLevel) }}
              </div>
              <div class="user-badges">
                <span v-if="user.isAdmin" class="badge admin-badge">
                  <i class="fas fa-crown"></i> 管理员
                </span>
                <span v-if="!user.isActive" class="badge inactive-badge">
                  <i class="fas fa-ban"></i> 已禁用
                </span>
                <span v-if="user.isValidMember" class="badge member-badge">
                  <i class="fas fa-star"></i> 有效会员
                </span>
              </div>
            </div>
            <div class="user-actions">
              <button class="action-button edit-button" @click.stop="editUser(user)" title="编辑用户">
                <i class="fas fa-edit"></i>
              </button>
              <button
                v-if="!user.isAdmin && user.id !== currentUserId"
                class="action-button delete-button"
                @click.stop="confirmDeleteUser(user)"
                title="删除用户"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.pages > 1" class="pagination">
          <button
            class="page-button"
            :disabled="pagination.page <= 1"
            @click="changePage(pagination.page - 1)"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          <span class="page-info">
            {{ pagination.page }} / {{ pagination.pages }}
          </span>
          <button
            class="page-button"
            :disabled="pagination.page >= pagination.pages"
            @click="changePage(pagination.page + 1)"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </main>

    <!-- 用户编辑弹窗 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑用户</h3>
          <button class="close-button" @click="closeEditModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>用户名</label>
            <input type="text" :value="selectedUser.username" readonly />
          </div>
          <div class="form-group">
            <label>邮箱</label>
            <input type="text" :value="selectedUser.email" readonly />
          </div>
          <div class="form-group">
            <label>UID</label>
            <input type="text" :value="selectedUser.uid" readonly />
          </div>
          <div class="form-group">
            <label>会员等级</label>
            <select v-model="editForm.membershipLevel">
              <option value="none">无会员</option>
              <option value="monthly">月会员</option>
              <option value="quarterly">季会员</option>
              <option value="yearly">年会员</option>
              <option value="lifetime">永久会员</option>
            </select>
          </div>
          <div v-if="editForm.membershipLevel !== 'none' && editForm.membershipLevel !== 'lifetime'" class="form-group">
            <label>会员天数</label>
            <input
              type="number"
              v-model="editForm.duration"
              placeholder="自定义天数（可选）"
              min="1"
            />
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                v-model="editForm.isActive"
              />
              <span>账户激活</span>
            </label>
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                v-model="editForm.isAdmin"
                :disabled="selectedUser.id === currentUserId"
              />
              <span>管理员权限</span>
            </label>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-button" @click="closeEditModal">取消</button>
          <button class="save-button" @click="saveUser" :disabled="saving">
            <i v-if="saving" class="fas fa-spinner fa-spin"></i>
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="closeDeleteModal">
      <div class="modal-content delete-modal" @click.stop>
        <div class="modal-header">
          <h3>确认删除用户</h3>
          <button class="close-button" @click="closeDeleteModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="delete-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <p>您确定要删除以下用户吗？此操作不可撤销！</p>
          </div>
          <div class="user-info-summary">
            <div class="info-item">
              <label>用户名：</label>
              <span>{{ userToDelete.username }}</span>
            </div>
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ userToDelete.email }}</span>
            </div>
            <div class="info-item">
              <label>UID：</label>
              <span>{{ userToDelete.uid }}</span>
            </div>
            <div class="info-item">
              <label>会员等级：</label>
              <span>{{ getMembershipLevelName(userToDelete.membershipLevel) }}</span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-button" @click="closeDeleteModal">取消</button>
          <button class="delete-confirm-button" @click="deleteUser" :disabled="deleting">
            <i v-if="deleting" class="fas fa-spinner fa-spin"></i>
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'UserManagementView',
  setup() {
    const router = useRouter()

    // 响应式数据
    const users = ref([])
    const loading = ref(false)
    const searchQuery = ref('')
    const pagination = ref({
      page: 1,
      limit: 20,
      total: 0,
      pages: 0
    })

    // 编辑相关
    const showEditModal = ref(false)
    const selectedUser = ref({})
    const editForm = ref({
      membershipLevel: 'none',
      duration: null,
      isActive: true,
      isAdmin: false
    })
    const saving = ref(false)

    // 删除相关
    const showDeleteModal = ref(false)
    const userToDelete = ref({})
    const deleting = ref(false)

    // 当前用户ID（防止管理员取消自己的权限）
    const currentUserId = computed(() => {
      const userStr = localStorage.getItem('user')
      const user = userStr ? JSON.parse(userStr) : null
      return user?._id || user?.id
    })

    // 搜索防抖
    let searchTimeout = null
    const handleSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        pagination.value.page = 1
        loadUsers()
      }, 500)
    }

    // 加载用户列表
    const loadUsers = async () => {
      try {
        loading.value = true
        const token = localStorage.getItem('token')

        const params = {
          page: pagination.value.page,
          limit: pagination.value.limit
        }

        if (searchQuery.value.trim()) {
          params.search = searchQuery.value.trim()
        }

        const response = await axios.get('/api/user/admin/users', {
          headers: { Authorization: `Bearer ${token}` },
          params
        })

        if (response.data.success) {
          users.value = response.data.data.users
          pagination.value = response.data.data.pagination
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        if (error.response?.status === 403) {
          alert('需要管理员权限')
          router.push({ name: 'user' })
        } else {
          alert('加载用户列表失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 分页
    const changePage = (page) => {
      pagination.value.page = page
      loadUsers()
    }

    // 选择用户
    const selectUser = (user) => {
      console.log('选择用户:', user)
    }

    // 编辑用户
    const editUser = (user) => {
      selectedUser.value = user
      editForm.value = {
        membershipLevel: user.membershipLevel,
        duration: null,
        isActive: user.isActive,
        isAdmin: user.isAdmin
      }
      showEditModal.value = true
    }

    // 关闭编辑弹窗
    const closeEditModal = () => {
      showEditModal.value = false
      selectedUser.value = {}
      editForm.value = {
        membershipLevel: 'none',
        duration: null,
        isActive: true,
        isAdmin: false
      }
    }

    // 保存用户
    const saveUser = async () => {
      try {
        saving.value = true
        const token = localStorage.getItem('token')

        // 更新会员等级
        if (editForm.value.membershipLevel !== selectedUser.value.membershipLevel) {
          await axios.put(`/api/user/admin/users/${selectedUser.value.id}/membership`, {
            membershipLevel: editForm.value.membershipLevel,
            duration: editForm.value.duration
          }, {
            headers: { Authorization: `Bearer ${token}` }
          })
        }

        // 更新用户状态
        if (editForm.value.isActive !== selectedUser.value.isActive ||
            editForm.value.isAdmin !== selectedUser.value.isAdmin) {
          await axios.put(`/api/user/admin/users/${selectedUser.value.id}/status`, {
            isActive: editForm.value.isActive,
            isAdmin: editForm.value.isAdmin
          }, {
            headers: { Authorization: `Bearer ${token}` }
          })
        }

        alert('用户信息更新成功')
        closeEditModal()
        loadUsers()
      } catch (error) {
        console.error('保存用户失败:', error)
        alert(error.response?.data?.error || '保存用户失败')
      } finally {
        saving.value = false
      }
    }

    // 获取会员等级名称
    const getMembershipLevelName = (level) => {
      const names = {
        none: '无会员',
        monthly: '月会员',
        quarterly: '季会员',
        yearly: '年会员',
        lifetime: '永久会员'
      }
      return names[level] || '未知'
    }

    // 获取会员等级样式类
    const getMembershipClass = (level) => {
      return `membership-${level}`
    }

    // 确认删除用户
    const confirmDeleteUser = (user) => {
      userToDelete.value = user
      showDeleteModal.value = true
    }

    // 关闭删除弹窗
    const closeDeleteModal = () => {
      showDeleteModal.value = false
      userToDelete.value = {}
    }

    // 删除用户
    const deleteUser = async () => {
      try {
        deleting.value = true
        const token = localStorage.getItem('token')

        await axios.delete(`/api/user/admin/users/${userToDelete.value.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        })

        alert('用户删除成功')
        closeDeleteModal()
        loadUsers() // 重新加载用户列表
      } catch (error) {
        console.error('删除用户失败:', error)
        alert(error.response?.data?.error || '删除用户失败')
      } finally {
        deleting.value = false
      }
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    // 页面加载时获取用户列表
    onMounted(() => {
      loadUsers()
    })

    return {
      users,
      loading,
      searchQuery,
      pagination,
      showEditModal,
      selectedUser,
      editForm,
      saving,
      showDeleteModal,
      userToDelete,
      deleting,
      currentUserId,
      handleSearch,
      loadUsers,
      changePage,
      selectUser,
      editUser,
      closeEditModal,
      saveUser,
      confirmDeleteUser,
      closeDeleteModal,
      deleteUser,
      getMembershipLevelName,
      getMembershipClass,
      goBack
    }
  }
}
</script>

<style scoped>
.user-management-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 80px;
}

/* 顶部导航栏 */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 15px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.back-button {
  position: absolute;
  left: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
}

.header h1 {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 搜索区域 */
.search-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.search-box {
  flex: 1;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
}

.search-box input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: white;
  font-size: 1rem;
  outline: none;
}

.search-box input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.refresh-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.refresh-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 用户列表区域 */
.users-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.loading-section,
.empty-section {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-section i,
.empty-section i {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.user-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.user-info {
  flex: 1;
  color: white;
}

.user-name {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.user-email {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 3px;
}

.user-uid {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.user-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.membership-level {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-align: center;
  min-width: 60px;
}

.membership-none {
  background: rgba(128, 128, 128, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.membership-monthly {
  background: rgba(52, 152, 219, 0.3);
  color: #3498db;
}

.membership-quarterly {
  background: rgba(155, 89, 182, 0.3);
  color: #9b59b6;
}

.membership-yearly {
  background: rgba(230, 126, 34, 0.3);
  color: #e67e22;
}

.membership-lifetime {
  background: rgba(241, 196, 15, 0.3);
  color: #f1c40f;
}

.user-badges {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 3px;
}

.admin-badge {
  background: rgba(231, 76, 60, 0.3);
  color: #e74c3c;
}

.inactive-badge {
  background: rgba(149, 165, 166, 0.3);
  color: #95a5a6;
}

.member-badge {
  background: rgba(46, 204, 113, 0.3);
  color: #2ecc71;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.edit-button:hover {
  background: rgba(52, 152, 219, 0.3);
  color: #3498db;
}

.delete-button:hover {
  background: rgba(231, 76, 60, 0.3);
  color: #e74c3c;
}

/* 分页 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  color: white;
}

.page-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-weight: bold;
  min-width: 80px;
  text-align: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #f5f5f5;
  color: #666;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input[readonly] {
  background: #f8f9fa;
  color: #666;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.checkbox-label span {
  color: #333;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 20px 25px;
  border-top: 1px solid #eee;
}

.cancel-button,
.save-button {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-button {
  background: #f8f9fa;
  color: #666;
}

.cancel-button:hover {
  background: #e9ecef;
}

.save-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.save-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 删除弹窗特殊样式 */
.delete-modal {
  max-width: 450px;
}

.delete-warning {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #e74c3c;
}

.delete-warning i {
  font-size: 2rem;
  color: #e74c3c;
}

.delete-warning p {
  margin: 0;
  color: #e74c3c;
  font-weight: 500;
}

.user-info-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #666;
}

.info-item span {
  color: #333;
  font-weight: 500;
}

.delete-confirm-button {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  background: #e74c3c;
  color: white;
}

.delete-confirm-button:hover:not(:disabled) {
  background: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.delete-confirm-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 15px;
  }

  .search-section {
    flex-direction: column;
    gap: 10px;
  }

  .user-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .user-status {
    align-items: flex-start;
    width: 100%;
  }

  .user-badges {
    justify-content: flex-start;
  }

  .modal-content {
    margin: 10px;
    max-width: none;
  }

  .modal-footer {
    flex-direction: column;
  }

  .cancel-button,
  .save-button {
    width: 100%;
    justify-content: center;
  }
}
</style>
