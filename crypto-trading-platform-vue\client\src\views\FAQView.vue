<template>
  <div class="faq-view">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <button class="back-button" @click="goBack">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1>常见问题</h1>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 搜索框 -->
      <div class="search-section">
        <div class="search-container">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索问题..."
            class="search-input"
          >
          <i class="fas fa-search search-icon"></i>
        </div>
      </div>

      <!-- 分类标签 -->
      <div class="category-tabs">
        <button
          v-for="category in categories"
          :key="category.id"
          :class="['category-tab', { active: activeCategory === category.id }]"
          @click="activeCategory = category.id"
        >
          <i :class="category.icon"></i>
          {{ category.name }}
        </button>
      </div>

      <!-- FAQ列表 -->
      <div class="faq-list">
        <div
          v-for="faq in filteredFAQs"
          :key="faq.id"
          class="faq-item"
          :class="{ expanded: expandedItems.includes(faq.id) }"
        >
          <div class="faq-question" @click="toggleExpand(faq.id)">
            <div class="question-content">
              <i :class="faq.icon" class="question-icon"></i>
              <span class="question-text">{{ faq.question }}</span>
            </div>
            <i class="fas fa-chevron-down expand-icon"></i>
          </div>

          <div class="faq-answer" v-show="expandedItems.includes(faq.id)">
            <div class="answer-content" v-html="faq.answer"></div>
            <div v-if="faq.tips" class="tips-section">
              <h4><i class="fas fa-lightbulb"></i> 小贴士</h4>
              <ul>
                <li v-for="tip in faq.tips" :key="tip">{{ tip }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系客服 -->
      <div class="contact-section">
        <div class="contact-card">
          <div class="contact-header">
            <i class="fas fa-headset"></i>
            <h3>还有其他问题？</h3>
          </div>
          <p>如果您没有找到想要的答案，请联系我们的客服团队</p>
          <button class="contact-button" @click="goToContactService">
            <i class="fas fa-comments"></i>
            联系客服
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'FAQView',
  setup() {
    const router = useRouter()
    const searchQuery = ref('')
    const activeCategory = ref('all')
    const expandedItems = ref([])

    // 分类
    const categories = ref([
      { id: 'all', name: '全部', icon: 'fas fa-list' },
      { id: 'trading', name: '交易相关', icon: 'fas fa-chart-line' },
      { id: 'security', name: '安全问题', icon: 'fas fa-shield-alt' },
      { id: 'account', name: '账户管理', icon: 'fas fa-user-cog' },
      { id: 'technical', name: '技术问题', icon: 'fas fa-cogs' }
    ])

    // FAQ数据
    const faqs = ref([
      {
        id: 1,
        category: 'trading',
        icon: 'fas fa-chart-line',
        question: '什么是加密货币交易？',
        answer: `
          <p>加密货币交易是指买卖数字货币（如比特币、以太坊等）以获取利润的活动。主要包括：</p>
          <ul>
            <li><strong>现货交易</strong>：直接买卖加密货币</li>
            <li><strong>合约交易</strong>：通过杠杆进行保证金交易</li>
            <li><strong>期货交易</strong>：约定未来某个时间点的交易</li>
          </ul>
        `,
        tips: [
          '新手建议从现货交易开始',
          '合约交易风险较高，需要充分了解后再参与',
          '建议制定交易计划和风险管理策略'
        ]
      },
      {
        id: 2,
        category: 'trading',
        icon: 'fas fa-percentage',
        question: '什么是杠杆交易？风险如何？',
        answer: `
          <p>杠杆交易允许您用较少的资金控制更大的交易头寸：</p>
          <ul>
            <li><strong>杠杆倍数</strong>：如10倍杠杆意味着用1000元可以控制10000元的头寸</li>
            <li><strong>收益放大</strong>：盈利时收益会按杠杆倍数放大</li>
            <li><strong>风险放大</strong>：亏损时损失也会按杠杆倍数放大</li>
            <li><strong>强制平仓</strong>：当亏损达到一定比例时会被强制平仓</li>
          </ul>
        `,
        tips: [
          '新手建议使用低杠杆（2-5倍）',
          '设置止损止盈，控制风险',
          '不要投入超过承受能力的资金'
        ]
      },
      {
        id: 3,
        category: 'trading',
        icon: 'fas fa-stop-circle',
        question: '如何设置止损止盈？',
        answer: `
          <p>止损止盈是风险管理的重要工具：</p>
          <ul>
            <li><strong>止损（Stop Loss）</strong>：当价格达到设定的亏损点时自动平仓</li>
            <li><strong>止盈（Take Profit）</strong>：当价格达到设定的盈利点时自动平仓</li>
            <li><strong>移动止损</strong>：随着价格有利变动而调整的止损点</li>
          </ul>
          <p>建议止损设置在2-5%，止盈设置在5-10%的范围内。</p>
        `,
        tips: [
          '严格执行止损，不要心存侥幸',
          '根据市场波动性调整止损止盈幅度',
          '可以分批止盈，降低风险'
        ]
      },
      {
        id: 4,
        category: 'security',
        icon: 'fas fa-key',
        question: 'API密钥安全吗？如何保护？',
        answer: `
          <p>API密钥的安全性至关重要：</p>
          <ul>
            <li><strong>权限设置</strong>：只开启必要的权限（如只读、交易权限）</li>
            <li><strong>IP白名单</strong>：限制API只能从特定IP地址访问</li>
            <li><strong>定期更换</strong>：建议定期更换API密钥</li>
            <li><strong>安全存储</strong>：不要在不安全的地方存储密钥</li>
          </ul>
        `,
        tips: [
          '不要开启提币权限除非必要',
          '使用强密码保护交易所账户',
          '开启双重认证(2FA)'
        ]
      },
      {
        id: 5,
        category: 'trading',
        icon: 'fas fa-coins',
        question: '什么是现货交易和合约交易的区别？',
        answer: `
          <p>现货交易和合约交易是两种不同的交易方式：</p>
          <h4>现货交易：</h4>
          <ul>
            <li><strong>实际持有</strong>：买入后实际拥有加密货币</li>
            <li><strong>无杠杆</strong>：使用自有资金进行交易</li>
            <li><strong>风险较低</strong>：最大损失为投入本金</li>
            <li><strong>适合长期持有</strong>：可以长期持有等待升值</li>
          </ul>
          <h4>合约交易：</h4>
          <ul>
            <li><strong>保证金交易</strong>：只需少量保证金即可开仓</li>
            <li><strong>支持杠杆</strong>：可以放大收益和风险</li>
            <li><strong>双向交易</strong>：可以做多或做空</li>
            <li><strong>有到期时间</strong>：部分合约有到期日</li>
          </ul>
        `,
        tips: [
          '新手建议从现货交易开始学习',
          '合约交易需要更多的市场知识和风险管理',
          '不要用全部资金进行合约交易'
        ]
      },
      {
        id: 6,
        category: 'account',
        icon: 'fas fa-user-shield',
        question: '如何保护我的交易账户安全？',
        answer: `
          <p>保护交易账户安全的重要措施：</p>
          <ul>
            <li><strong>强密码</strong>：使用复杂且唯一的密码</li>
            <li><strong>双重认证</strong>：开启Google Authenticator或短信验证</li>
            <li><strong>邮箱安全</strong>：确保注册邮箱的安全性</li>
            <li><strong>定期检查</strong>：定期查看账户登录记录</li>
            <li><strong>官方渠道</strong>：只通过官方网站或APP登录</li>
            <li><strong>避免公共WiFi</strong>：不在公共网络进行交易</li>
          </ul>
        `,
        tips: [
          '不要在任何地方分享你的登录信息',
          '警惕钓鱼网站和虚假APP',
          '定期更新密码和安全设置'
        ]
      },
      {
        id: 7,
        category: 'technical',
        icon: 'fas fa-chart-bar',
        question: '什么是技术分析？如何使用？',
        answer: `
          <p>技术分析是通过研究价格图表和交易量来预测未来价格走势的方法：</p>
          <ul>
            <li><strong>K线图</strong>：显示开盘价、收盘价、最高价、最低价</li>
            <li><strong>移动平均线</strong>：平滑价格波动，显示趋势方向</li>
            <li><strong>支撑阻力位</strong>：价格可能反弹或回落的关键位置</li>
            <li><strong>技术指标</strong>：如RSI、MACD、布林带等</li>
            <li><strong>成交量分析</strong>：确认价格走势的有效性</li>
          </ul>
        `,
        tips: [
          '技术分析不是100%准确的预测工具',
          '结合基本面分析使用效果更好',
          '多个指标相互验证可提高准确性'
        ]
      },
      {
        id: 8,
        category: 'trading',
        icon: 'fas fa-exchange-alt',
        question: '什么是滑点？如何减少滑点影响？',
        answer: `
          <p>滑点是指订单执行价格与预期价格之间的差异：</p>
          <ul>
            <li><strong>市场波动</strong>：价格快速变动导致的滑点</li>
            <li><strong>流动性不足</strong>：买卖盘深度不够造成的滑点</li>
            <li><strong>订单大小</strong>：大额订单更容易产生滑点</li>
          </ul>
          <h4>减少滑点的方法：</h4>
          <ul>
            <li><strong>使用限价单</strong>：设定具体的执行价格</li>
            <li><strong>分批交易</strong>：将大额订单分成小额执行</li>
            <li><strong>选择流动性好的时段</strong>：避免在市场清淡时交易</li>
            <li><strong>选择主流币种</strong>：流动性更好，滑点更小</li>
          </ul>
        `,
        tips: [
          '市价单容易产生滑点，谨慎使用',
          '在重要新闻发布前后避免大额交易',
          '了解不同交易所的流动性情况'
        ]
      },
      {
        id: 9,
        category: 'security',
        icon: 'fas fa-exclamation-triangle',
        question: '如何识别和避免加密货币诈骗？',
        answer: `
          <p>常见的加密货币诈骗类型和防范方法：</p>
          <h4>常见诈骗类型：</h4>
          <ul>
            <li><strong>钓鱼网站</strong>：模仿正规交易所的虚假网站</li>
            <li><strong>庞氏骗局</strong>：承诺高收益的投资项目</li>
            <li><strong>假冒客服</strong>：冒充官方客服索要密码</li>
            <li><strong>虚假空投</strong>：要求先转币才能获得空投</li>
          </ul>
          <h4>防范措施：</h4>
          <ul>
            <li><strong>验证网址</strong>：确保使用官方正确的网址</li>
            <li><strong>不轻信高收益</strong>：警惕过高的投资回报承诺</li>
            <li><strong>保护私钥</strong>：永远不要向任何人透露私钥</li>
            <li><strong>官方渠道</strong>：只通过官方渠道联系客服</li>
          </ul>
        `,
        tips: [
          '如果看起来太好以至于不真实，那可能就是骗局',
          '在投资前做充分的研究和调查',
          '不要因为FOMO（害怕错过）而匆忙投资'
        ]
      },
      {
        id: 10,
        category: 'account',
        icon: 'fas fa-money-bill-wave',
        question: '如何进行充值和提现？',
        answer: `
          <p>充值和提现是交易的基础操作：</p>
          <h4>充值流程：</h4>
          <ul>
            <li><strong>选择币种</strong>：选择要充值的加密货币</li>
            <li><strong>获取地址</strong>：复制交易所提供的充值地址</li>
            <li><strong>选择网络</strong>：确认使用正确的区块链网络</li>
            <li><strong>发送转账</strong>：从钱包或其他交易所转入</li>
            <li><strong>等待确认</strong>：等待区块链网络确认</li>
          </ul>
          <h4>提现流程：</h4>
          <ul>
            <li><strong>添加地址</strong>：添加并验证提现地址</li>
            <li><strong>输入金额</strong>：输入要提现的数量</li>
            <li><strong>支付手续费</strong>：确认网络手续费</li>
            <li><strong>安全验证</strong>：完成邮箱、短信等验证</li>
          </ul>
        `,
        tips: [
          '首次提现建议先小额测试',
          '仔细核对提现地址，避免资产丢失',
          '了解不同网络的手续费差异'
        ]
      },
      {
        id: 11,
        category: 'technical',
        icon: 'fas fa-wifi',
        question: '交易时网络连接不稳定怎么办？',
        answer: `
          <p>网络连接问题可能影响交易执行，以下是解决方案：</p>
          <ul>
            <li><strong>检查网络</strong>：确保网络连接稳定</li>
            <li><strong>使用有线连接</strong>：有线网络比WiFi更稳定</li>
            <li><strong>备用网络</strong>：准备手机热点作为备用</li>
            <li><strong>避免高峰期</strong>：避开网络拥堵时段</li>
            <li><strong>使用限价单</strong>：减少网络延迟的影响</li>
          </ul>
        `,
        tips: [
          '重要交易前先测试网络连接',
          '保持交易设备的网络优化',
          '考虑使用专业的交易网络环境'
        ]
      },
      {
        id: 12,
        category: 'trading',
        icon: 'fas fa-robot',
        question: '什么是量化交易？适合新手吗？',
        answer: `
          <p>量化交易是使用算法和数学模型进行自动化交易：</p>
          <h4>量化交易特点：</h4>
          <ul>
            <li><strong>自动执行</strong>：根据预设策略自动买卖</li>
            <li><strong>情绪控制</strong>：避免人为情绪影响决策</li>
            <li><strong>24小时运行</strong>：可以全天候监控市场</li>
            <li><strong>回测验证</strong>：可以用历史数据验证策略</li>
          </ul>
          <h4>适用人群：</h4>
          <ul>
            <li><strong>有编程基础</strong>：能够理解和调整策略参数</li>
            <li><strong>风险承受能力</strong>：理解策略可能的风险</li>
            <li><strong>持续学习</strong>：愿意不断优化和改进策略</li>
          </ul>
        `,
        tips: [
          '新手建议先学习基础交易知识',
          '从简单的策略开始，逐步深入',
          '充分测试策略后再投入真实资金'
        ]
      },
      {
        id: 13,
        category: 'account',
        icon: 'fas fa-calculator',
        question: '如何计算交易手续费？',
        answer: `
          <p>了解交易手续费的计算方法：</p>
          <h4>现货交易手续费：</h4>
          <ul>
            <li><strong>Maker费率</strong>：提供流动性的订单（限价单）</li>
            <li><strong>Taker费率</strong>：消耗流动性的订单（市价单）</li>
            <li><strong>计算公式</strong>：交易金额 × 费率</li>
          </ul>
          <h4>合约交易手续费：</h4>
          <ul>
            <li><strong>开仓手续费</strong>：建立仓位时收取</li>
            <li><strong>平仓手续费</strong>：平仓时收取</li>
            <li><strong>资金费率</strong>：持仓过夜可能产生的费用</li>
          </ul>
          <p>不同VIP等级享有不同的费率优惠。</p>
        `,
        tips: [
          '高频交易要特别关注手续费成本',
          '考虑使用平台币抵扣手续费',
          '提升VIP等级可以降低手续费'
        ]
      }
    ])

    // 计算过滤后的FAQ
    const filteredFAQs = computed(() => {
      let filtered = faqs.value

      // 按分类过滤
      if (activeCategory.value !== 'all') {
        filtered = filtered.filter(faq => faq.category === activeCategory.value)
      }

      // 按搜索关键词过滤
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(faq =>
          faq.question.toLowerCase().includes(query) ||
          faq.answer.toLowerCase().includes(query)
        )
      }

      return filtered
    })

    // 切换展开/收起
    const toggleExpand = (id) => {
      const index = expandedItems.value.indexOf(id)
      if (index > -1) {
        expandedItems.value.splice(index, 1)
      } else {
        expandedItems.value.push(id)
      }
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    // 跳转到联系客服
    const goToContactService = () => {
      router.push({ name: 'contact-service' })
    }

    return {
      searchQuery,
      activeCategory,
      expandedItems,
      categories,
      faqs,
      filteredFAQs,
      toggleExpand,
      goBack,
      goToContactService
    }
  }
}
</script>

<style scoped>
.faq-view {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
  padding: 15px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.3s;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.3rem;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 25px;
}

.search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 20px;
  border: none;
  border-radius: 25px;
  background: white;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  font-size: 1rem;
  outline: none;
  transition: box-shadow 0.3s;
}

.search-input:focus {
  box-shadow: 0 4px 20px rgba(10, 110, 106, 0.2);
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 1.1rem;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 25px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.category-tab {
  background: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.category-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.category-tab.active {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
}

/* FAQ列表 */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.faq-item {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: all 0.3s;
}

.faq-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0,0,0,0.15);
}

.faq-question {
  padding: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background 0.3s;
}

.faq-question:hover {
  background: #f8f9fa;
}

.question-content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.question-icon {
  color: #0a6e6a;
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.question-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.expand-icon {
  color: #666;
  font-size: 1rem;
  transition: transform 0.3s;
}

.faq-item.expanded .expand-icon {
  transform: rotate(180deg);
}

.faq-answer {
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

.answer-content {
  padding: 25px;
  color: #555;
  line-height: 1.6;
}

.answer-content p {
  margin-bottom: 15px;
}

.answer-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.answer-content li {
  margin-bottom: 8px;
}

.answer-content strong {
  color: #0a6e6a;
  font-weight: 600;
}

.tips-section {
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #e8f5f3 0%, #d4f1ec 100%);
  border-radius: 10px;
  border-left: 4px solid #0a6e6a;
}

.tips-section h4 {
  color: #0a6e6a;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
}

.tips-section ul {
  margin: 0;
  padding-left: 20px;
}

.tips-section li {
  color: #2c5530;
  margin-bottom: 8px;
}

/* 联系客服区域 */
.contact-section {
  margin-top: 40px;
}

.contact-card {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 30px rgba(10, 110, 106, 0.3);
}

.contact-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.contact-header i {
  font-size: 2rem;
  color: #ffd700;
}

.contact-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.contact-card p {
  margin-bottom: 25px;
  opacity: 0.9;
  font-size: 1.1rem;
}

.contact-button {
  background: #ffd700;
  color: #333;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.contact-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 15px;
  }

  .category-tabs {
    gap: 8px;
  }

  .category-tab {
    padding: 10px 15px;
    font-size: 0.85rem;
  }

  .faq-question {
    padding: 15px;
  }

  .question-text {
    font-size: 1rem;
  }

  .answer-content {
    padding: 20px;
  }

  .contact-card {
    padding: 25px 20px;
  }

  .contact-header h3 {
    font-size: 1.3rem;
  }
}
</style>
