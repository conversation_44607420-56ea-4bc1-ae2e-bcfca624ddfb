<template>
  <div class="strategy-backtest-view">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <button class="back-button" @click="goBack">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1>策略回测</h1>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 介绍说明 -->
      <div class="intro-section">
        <div class="intro-card">
          <h2>📊 策略回测系统</h2>
          <p>使用历史数据验证交易策略的有效性，分析策略表现和风险指标，为实盘交易提供数据支持。</p>
          <div class="key-features">
            <div class="feature-item">
              <i class="fas fa-chart-area"></i>
              <span>历史回测</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-calculator"></i>
              <span>收益分析</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-shield-alt"></i>
              <span>风险评估</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 策略选择 -->
      <div class="strategy-selection">
        <h3>🎯 选择回测策略</h3>
        <div class="strategy-grid">
          <div
            v-for="strategy in strategies"
            :key="strategy.id"
            class="strategy-card"
            :class="{ selected: selectedStrategy === strategy.id }"
            @click="selectStrategy(strategy.id)"
          >
            <div class="strategy-header">
              <div class="strategy-icon">{{ strategy.icon }}</div>
              <div class="strategy-info">
                <h4>{{ strategy.name }}</h4>
                <p>{{ strategy.description }}</p>
              </div>
              <div class="strategy-type" :class="strategy.type">
                {{ strategy.typeLabel }}
              </div>
            </div>
            <div class="strategy-stats">
              <div class="stat-item">
                <span class="stat-label">历史胜率</span>
                <span class="stat-value">{{ strategy.winRate }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">年化收益</span>
                <span class="stat-value">{{ strategy.annualReturn }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 回测参数设置 -->
      <div v-if="selectedStrategy" class="backtest-settings">
        <h3>⚙️ 回测参数设置</h3>
        <div class="settings-grid">
          <div class="setting-group">
            <label>交易对</label>
            <select v-model="backtestParams.symbol" class="setting-input">
              <option value="BTC/USDT">BTC/USDT</option>
              <option value="ETH/USDT">ETH/USDT</option>
              <option value="BNB/USDT">BNB/USDT</option>
              <option value="ADA/USDT">ADA/USDT</option>
            </select>
          </div>

          <div class="setting-group">
            <label>时间周期</label>
            <select v-model="backtestParams.timeframe" class="setting-input">
              <option value="1m">1分钟</option>
              <option value="5m">5分钟</option>
              <option value="15m">15分钟</option>
              <option value="1h">1小时</option>
              <option value="4h">4小时</option>
              <option value="1d">1天</option>
            </select>
          </div>

          <div class="setting-group">
            <label>回测开始时间</label>
            <input
              v-model="backtestParams.startDate"
              type="date"
              class="setting-input"
            >
          </div>

          <div class="setting-group">
            <label>回测结束时间</label>
            <input
              v-model="backtestParams.endDate"
              type="date"
              class="setting-input"
            >
          </div>

          <div class="setting-group">
            <label>初始资金 (USDT)</label>
            <input
              v-model="backtestParams.initialCapital"
              type="number"
              class="setting-input"
              placeholder="10000"
            >
          </div>

          <div class="setting-group">
            <label>手续费率 (%)</label>
            <input
              v-model="backtestParams.feeRate"
              type="number"
              step="0.01"
              class="setting-input"
              placeholder="0.1"
            >
          </div>
        </div>

        <!-- 策略特定参数 -->
        <div v-if="selectedStrategyData" class="strategy-params">
          <h4>📈 策略参数</h4>
          <div class="params-grid">
            <div
              v-for="param in selectedStrategyData.parameters"
              :key="param.key"
              class="param-group"
            >
              <label>{{ param.label }}</label>
              <input
                v-model="backtestParams.strategyParams[param.key]"
                :type="param.type"
                :step="param.step"
                :min="param.min"
                :max="param.max"
                class="setting-input"
                :placeholder="param.default"
              >
              <small class="param-desc">{{ param.description }}</small>
            </div>
          </div>
        </div>

        <!-- 开始回测按钮 -->
        <div class="backtest-actions">
          <button
            class="start-backtest-btn"
            @click="startBacktest"
            :disabled="isBacktesting"
          >
            <i class="fas fa-play" v-if="!isBacktesting"></i>
            <i class="fas fa-spinner fa-spin" v-else></i>
            {{ isBacktesting ? '回测中...' : '开始回测' }}
          </button>
        </div>
      </div>

      <!-- 回测结果 -->
      <div v-if="backtestResults" class="backtest-results">
        <h3>📈 回测结果</h3>

        <!-- 关键指标 -->
        <div class="results-summary">
          <div class="result-card">
            <div class="result-icon">💰</div>
            <div class="result-info">
              <div class="result-label">总收益</div>
              <div class="result-value" :class="{ positive: backtestResults.totalReturn > 0, negative: backtestResults.totalReturn < 0 }">
                {{ backtestResults.totalReturn > 0 ? '+' : '' }}{{ backtestResults.totalReturn.toFixed(2) }}%
              </div>
            </div>
          </div>

          <div class="result-card">
            <div class="result-icon">🎯</div>
            <div class="result-info">
              <div class="result-label">胜率</div>
              <div class="result-value">{{ backtestResults.winRate.toFixed(1) }}%</div>
            </div>
          </div>

          <div class="result-card">
            <div class="result-icon">📊</div>
            <div class="result-info">
              <div class="result-label">夏普比率</div>
              <div class="result-value">{{ backtestResults.sharpeRatio.toFixed(2) }}</div>
            </div>
          </div>

          <div class="result-card">
            <div class="result-icon">📉</div>
            <div class="result-info">
              <div class="result-label">最大回撤</div>
              <div class="result-value negative">{{ backtestResults.maxDrawdown.toFixed(2) }}%</div>
            </div>
          </div>
        </div>

        <!-- 详细统计 -->
        <div class="detailed-stats">
          <h4>📋 详细统计</h4>
          <div class="stats-grid">
            <div class="stat-row">
              <span class="stat-label">交易次数</span>
              <span class="stat-value">{{ backtestResults.totalTrades }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">盈利交易</span>
              <span class="stat-value positive">{{ backtestResults.winningTrades }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">亏损交易</span>
              <span class="stat-value negative">{{ backtestResults.losingTrades }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">平均盈利</span>
              <span class="stat-value positive">{{ backtestResults.avgWin.toFixed(2) }}%</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">平均亏损</span>
              <span class="stat-value negative">{{ backtestResults.avgLoss.toFixed(2) }}%</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">盈亏比</span>
              <span class="stat-value">{{ backtestResults.profitLossRatio.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="result-actions">
          <button class="action-btn secondary" @click="exportResults">
            <i class="fas fa-download"></i>
            导出报告
          </button>
          <button class="action-btn primary" @click="applyStrategy">
            <i class="fas fa-rocket"></i>
            应用策略
          </button>
        </div>
      </div>

      <!-- 风险提醒 -->
      <div class="risk-warning">
        <h3>⚠️ 重要提醒</h3>
        <div class="warning-content">
          <div class="warning-item">
            <i class="fas fa-exclamation-triangle"></i>
            <span>回测结果基于历史数据，不代表未来表现</span>
          </div>
          <div class="warning-item">
            <i class="fas fa-chart-line"></i>
            <span>实盘交易可能面临滑点、手续费等额外成本</span>
          </div>
          <div class="warning-item">
            <i class="fas fa-shield-alt"></i>
            <span>建议先小资金测试，确认策略有效性后再加大投入</span>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'StrategyBacktestView',
  setup() {
    const router = useRouter()

    // 响应式数据
    const selectedStrategy = ref(null)
    const isBacktesting = ref(false)
    const backtestResults = ref(null)

    // 回测参数
    const backtestParams = ref({
      symbol: 'BTC/USDT',
      timeframe: '1h',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      initialCapital: 10000,
      feeRate: 0.1,
      strategyParams: {}
    })

    // 策略列表
    const strategies = ref([
      {
        id: 1,
        name: '趋势动量策略',
        icon: '📈',
        description: '基于移动平均线和动量指标的趋势跟踪策略',
        type: 'trend',
        typeLabel: '趋势',
        winRate: 65.2,
        annualReturn: 28.5,
        parameters: [
          { key: 'fastMA', label: '快速均线', type: 'number', default: 10, min: 5, max: 50, description: '短期移动平均线周期' },
          { key: 'slowMA', label: '慢速均线', type: 'number', default: 30, min: 20, max: 100, description: '长期移动平均线周期' },
          { key: 'rsiPeriod', label: 'RSI周期', type: 'number', default: 14, min: 10, max: 30, description: 'RSI指标计算周期' }
        ]
      }
    ])

    return {
      selectedStrategy,
      isBacktesting,
      backtestResults,
      backtestParams,
      strategies,
      goBack: () => router.go(-1),
      selectStrategy: (id) => { selectedStrategy.value = id },
      selectedStrategyData: computed(() => strategies.value.find(s => s.id === selectedStrategy.value)),
      startBacktest: () => {
        isBacktesting.value = true
        // 模拟回测过程
        setTimeout(() => {
          backtestResults.value = {
            totalReturn: 15.67,
            winRate: 62.5,
            sharpeRatio: 1.23,
            maxDrawdown: -8.45,
            totalTrades: 48,
            winningTrades: 30,
            losingTrades: 18,
            avgWin: 3.2,
            avgLoss: -1.8,
            profitLossRatio: 1.78
          }
          isBacktesting.value = false
        }, 3000)
      },
      exportResults: () => alert('导出功能开发中...'),
      applyStrategy: () => router.push({ name: 'binance' })
    }
  }
}
</script>

<style scoped>
.strategy-backtest-view {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
  padding: 15px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.3s;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.3rem;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 介绍区域 */
.intro-section {
  margin-bottom: 30px;
}

.intro-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
}

.intro-card h2 {
  margin: 0 0 15px 0;
  font-size: 1.8rem;
}

.intro-card p {
  margin-bottom: 25px;
  opacity: 0.9;
  font-size: 1.1rem;
  line-height: 1.6;
}

.key-features {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.feature-item i {
  font-size: 2rem;
  color: #ffd700;
}

.feature-item span {
  font-size: 0.9rem;
  font-weight: 500;
}

/* 策略选择 */
.strategy-selection {
  margin-bottom: 30px;
}

.strategy-selection h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.strategy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.strategy-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.strategy-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.strategy-card.selected {
  border-color: #0a6e6a;
  box-shadow: 0 8px 25px rgba(10, 110, 106, 0.3);
}

.strategy-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.strategy-icon {
  font-size: 2rem;
  width: 50px;
  text-align: center;
}

.strategy-info {
  flex: 1;
}

.strategy-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
}

.strategy-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.strategy-type {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  background: #e3f2fd;
  color: #1976d2;
}

.strategy-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #0a6e6a;
}

/* 回测设置 */
.backtest-settings {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.backtest-settings h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.setting-input {
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
  outline: none;
}

.setting-input:focus {
  border-color: #0a6e6a;
}

/* 策略参数 */
.strategy-params {
  margin-top: 25px;
  padding-top: 25px;
  border-top: 1px solid #eee;
}

.strategy-params h4 {
  color: #333;
  margin-bottom: 15px;
}

.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.param-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.param-desc {
  color: #666;
  font-size: 0.8rem;
}

/* 回测按钮 */
.backtest-actions {
  text-align: center;
  margin-top: 25px;
}

.start-backtest-btn {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.start-backtest-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(10, 110, 106, 0.3);
}

.start-backtest-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 回测结果 */
.backtest-results {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.backtest-results h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.3rem;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.result-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-left: 4px solid #0a6e6a;
}

.result-icon {
  font-size: 2rem;
  width: 50px;
  text-align: center;
}

.result-info {
  flex: 1;
}

.result-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.result-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.result-value.positive {
  color: #27ae60;
}

.result-value.negative {
  color: #e74c3c;
}

/* 详细统计 */
.detailed-stats {
  margin-bottom: 25px;
}

.detailed-stats h4 {
  color: #333;
  margin-bottom: 15px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-row .stat-label {
  color: #666;
  font-size: 0.9rem;
}

.stat-row .stat-value {
  font-weight: 600;
  color: #333;
}

.stat-row .stat-value.positive {
  color: #27ae60;
}

.stat-row .stat-value.negative {
  color: #e74c3c;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e0e0e0;
}

.action-btn.secondary:hover {
  background: #e9ecef;
  border-color: #ccc;
}

.action-btn.primary {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(10, 110, 106, 0.3);
}

/* 风险提醒 */
.risk-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 15px;
  padding: 25px;
  border-left: 4px solid #f39c12;
}

.risk-warning h3 {
  color: #d68910;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.warning-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #8b6914;
}

.warning-item i {
  color: #f39c12;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 15px;
  }

  .intro-card {
    padding: 25px 20px;
  }

  .key-features {
    gap: 20px;
  }

  .strategy-grid {
    grid-template-columns: 1fr;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .results-summary {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .action-btn {
    justify-content: center;
  }
}
</style>
