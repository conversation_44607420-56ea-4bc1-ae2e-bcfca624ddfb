const axios = require('axios');

async function testMembershipInfo() {
  try {
    console.log('测试会员信息API...');
    
    // 登录测试用户
    const loginResponse = await axios.post('http://localhost:3009/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    }, { timeout: 10000 });
    
    console.log('登录响应:', loginResponse.data);
    
    if (loginResponse.data.success) {
      const token = loginResponse.data.token;
      console.log('登录成功，获取到token');
      
      // 获取会员信息
      console.log('\n获取会员信息...');
      const membershipResponse = await axios.get('http://localhost:3009/api/membership/info', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('会员信息响应:', membershipResponse.data);
    }
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

testMembershipInfo();
