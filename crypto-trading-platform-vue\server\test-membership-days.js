const mongoose = require('mongoose');
const User = require('./models/User');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/crypto-trading-platform');
    console.log('✅ MongoDB连接成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }
};

// 测试会员天数计算
const testMembershipDays = async () => {
  try {
    console.log('=== 测试会员天数计算 ===\n');

    // 获取所有用户
    const users = await User.find({});
    console.log(`找到 ${users.length} 个用户\n`);

    for (const user of users) {
      console.log(`用户: ${user.username} (${user.email})`);
      console.log(`会员等级: ${user.membershipLevel}`);
      console.log(`会员开始时间: ${user.membershipStartDate}`);
      console.log(`会员到期时间: ${user.membershipExpiry}`);
      
      // 测试会员状态
      const isValidMember = user.isValidMember();
      console.log(`是否有效会员: ${isValidMember}`);
      
      // 测试剩余天数计算
      const daysLeft = user.getMembershipDaysLeft();
      console.log(`剩余天数: ${daysLeft === -1 ? '永久' : daysLeft}`);
      
      // 测试toPublicJSON方法
      const publicInfo = user.toPublicJSON();
      console.log(`公开信息中的剩余天数: ${publicInfo.membershipDaysLeft === -1 ? '永久' : publicInfo.membershipDaysLeft}`);
      
      console.log('---');
    }

    // 创建一个测试用户来验证天数计算
    console.log('\n=== 创建测试用户验证天数计算 ===');
    
    // 删除可能存在的测试用户
    await User.deleteOne({ email: '<EMAIL>' });
    
    // 创建一个月会员测试用户
    const testUser = new User({
      uid: User.generateUID(),
      email: '<EMAIL>',
      password: 'test123',
      username: 'daytest',
      membershipLevel: 'monthly',
      isActive: true,
      isAdmin: false
    });

    // 设置会员信息
    const now = new Date();
    testUser.membershipStartDate = now;
    
    // 设置到期时间为30天后
    const expiryDate = new Date(now);
    expiryDate.setDate(expiryDate.getDate() + 30);
    testUser.membershipExpiry = expiryDate;

    await testUser.save();
    
    console.log(`✅ 创建测试用户: ${testUser.username}`);
    console.log(`会员开始时间: ${testUser.membershipStartDate}`);
    console.log(`会员到期时间: ${testUser.membershipExpiry}`);
    console.log(`计算的剩余天数: ${testUser.getMembershipDaysLeft()}`);
    
    // 手动计算验证
    const manualNow = new Date();
    manualNow.setHours(0, 0, 0, 0);
    const manualExpiry = new Date(testUser.membershipExpiry);
    manualExpiry.setHours(0, 0, 0, 0);
    const manualDiff = Math.ceil((manualExpiry - manualNow) / (1000 * 60 * 60 * 24));
    console.log(`手动计算的剩余天数: ${Math.max(0, manualDiff)}`);
    
    // 测试过期用户
    console.log('\n=== 测试过期用户 ===');
    
    // 删除可能存在的过期测试用户
    await User.deleteOne({ email: '<EMAIL>' });
    
    const expiredUser = new User({
      uid: User.generateUID(),
      email: '<EMAIL>',
      password: 'test123',
      username: 'expiredtest',
      membershipLevel: 'monthly',
      isActive: true,
      isAdmin: false
    });

    // 设置过期时间为昨天
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    expiredUser.membershipStartDate = new Date(yesterday.getTime() - 30 * 24 * 60 * 60 * 1000);
    expiredUser.membershipExpiry = yesterday;

    await expiredUser.save();
    
    console.log(`✅ 创建过期测试用户: ${expiredUser.username}`);
    console.log(`会员到期时间: ${expiredUser.membershipExpiry}`);
    console.log(`是否有效会员: ${expiredUser.isValidMember()}`);
    console.log(`剩余天数: ${expiredUser.getMembershipDaysLeft()}`);
    
    // 测试永久会员
    console.log('\n=== 测试永久会员 ===');
    
    // 删除可能存在的永久测试用户
    await User.deleteOne({ email: '<EMAIL>' });
    
    const lifetimeUser = new User({
      uid: User.generateUID(),
      email: '<EMAIL>',
      password: 'test123',
      username: 'lifetimetest',
      membershipLevel: 'lifetime',
      isActive: true,
      isAdmin: false
    });

    lifetimeUser.membershipStartDate = new Date();
    // 永久会员没有到期时间
    lifetimeUser.membershipExpiry = null;

    await lifetimeUser.save();
    
    console.log(`✅ 创建永久会员测试用户: ${lifetimeUser.username}`);
    console.log(`会员等级: ${lifetimeUser.membershipLevel}`);
    console.log(`是否有效会员: ${lifetimeUser.isValidMember()}`);
    console.log(`剩余天数: ${lifetimeUser.getMembershipDaysLeft() === -1 ? '永久' : lifetimeUser.getMembershipDaysLeft()}`);

    console.log('\n✅ 会员天数计算测试完成');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await testMembershipDays();
  
  console.log('\n=== 测试完成 ===');
  process.exit(0);
};

// 运行测试
main().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
