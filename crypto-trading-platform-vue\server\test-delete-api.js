const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3009/api';

// 测试删除用户API
const testDeleteUserAPI = async () => {
  try {
    console.log('=== 测试删除用户API ===\n');

    // 1. 首先登录获取管理员token
    console.log('1. 管理员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 管理员登录失败:', loginResponse.data.error);
      return;
    }

    const token = loginResponse.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    console.log('✅ 管理员登录成功');

    // 2. 获取用户列表
    console.log('\n2. 获取用户列表...');
    const usersResponse = await axios.get(`${BASE_URL}/user/admin/users`, { headers });
    
    if (usersResponse.data.success) {
      const users = usersResponse.data.data.users;
      console.log(`✅ 获取用户列表成功 (${users.length} 个用户):`);
      users.forEach(user => {
        console.log(`  - ${user.username} (${user.email}) - 管理员: ${user.isAdmin} - 会员: ${user.membershipLevel}`);
      });
    } else {
      console.error('❌ 获取用户列表失败:', usersResponse.data.error);
      return;
    }

    // 3. 测试删除管理员用户（应该失败）
    console.log('\n3. 测试删除管理员用户（安全检查）...');
    const adminUser = usersResponse.data.data.users.find(u => u.isAdmin);
    if (adminUser) {
      try {
        await axios.delete(`${BASE_URL}/user/admin/users/${adminUser.id}`, { headers });
        console.error('❌ 安全检查失败：不应该允许删除管理员用户');
      } catch (error) {
        if (error.response?.status === 400) {
          console.log('✅ 安全检查通过：正确拒绝删除管理员用户');
          console.log(`   错误信息: ${error.response.data.error}`);
        } else {
          console.error('❌ 删除管理员用户测试异常:', error.response?.data || error.message);
        }
      }
    }

    // 4. 测试删除普通用户
    console.log('\n4. 测试删除普通用户...');
    const regularUser = usersResponse.data.data.users.find(u => !u.isAdmin);
    if (regularUser) {
      console.log(`选择删除用户: ${regularUser.username} (${regularUser.email})`);
      
      try {
        const deleteResponse = await axios.delete(`${BASE_URL}/user/admin/users/${regularUser.id}`, { headers });
        
        if (deleteResponse.data.success) {
          console.log('✅ 删除普通用户成功');
          console.log(`   删除的用户: ${deleteResponse.data.data.deletedUser.username}`);
          console.log(`   响应消息: ${deleteResponse.data.message}`);
          
          // 验证用户确实被删除了
          try {
            await axios.get(`${BASE_URL}/user/admin/users/${regularUser.id}`, { headers });
            console.error('❌ 删除验证失败：用户仍然存在');
          } catch (error) {
            if (error.response?.status === 404) {
              console.log('✅ 删除验证通过：用户已被成功删除');
            } else {
              console.error('❌ 删除验证异常:', error.response?.data || error.message);
            }
          }
          
          // 再次获取用户列表确认
          console.log('\n5. 确认用户列表更新...');
          const updatedUsersResponse = await axios.get(`${BASE_URL}/user/admin/users`, { headers });
          if (updatedUsersResponse.data.success) {
            const updatedUsers = updatedUsersResponse.data.data.users;
            console.log(`✅ 更新后的用户列表 (${updatedUsers.length} 个用户):`);
            updatedUsers.forEach(user => {
              console.log(`  - ${user.username} (${user.email}) - 管理员: ${user.isAdmin}`);
            });
          }
        } else {
          console.error('❌ 删除用户失败:', deleteResponse.data.error);
        }
      } catch (error) {
        console.error('❌ 删除普通用户失败:', error.response?.data || error.message);
      }
    } else {
      console.log('⚠️  没有找到可删除的普通用户');
    }

    console.log('\n✅ 删除用户API测试完成');

  } catch (error) {
    console.error('❌ API测试过程中出错:', error.response?.data || error.message);
  }
};

// 运行测试
testDeleteUserAPI().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
