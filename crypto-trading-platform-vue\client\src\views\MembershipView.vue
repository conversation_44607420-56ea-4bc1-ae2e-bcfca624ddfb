<template>
  <div class="membership-view">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <button class="back-button" @click="goBack">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1>开通会员</h1>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 会员介绍 -->
      <div class="intro-section">
        <div class="intro-card">
          <h2>🎯 会员特权</h2>
          <p>升级会员，解锁更多策略权限和专属服务，让您的交易更加高效便捷！</p>
        </div>
      </div>

      <!-- 会员等级卡片 -->
      <div class="membership-cards" v-if="membershipConfigs.length > 0">
        <div
          class="membership-card"
          v-for="config in membershipConfigs"
          :key="config.level"
          :class="{ 'popular': config.level === 'yearly' }"
        >
          <!-- 推荐标签 -->
          <div v-if="config.level === 'yearly'" class="popular-badge">
            推荐
          </div>

          <div class="card-header">
            <h3>{{ config.name }}</h3>
            <div class="price-section">
              <div class="current-price">¥{{ config.price }}</div>
              <div class="original-price" v-if="config.originalPrice > config.price">
                原价: ¥{{ config.originalPrice }}
              </div>
            </div>
          </div>

          <div class="features-section">
            <div class="feature-category">
              <h4>📊 策略权限</h4>
              <p v-if="config.features.strategies.contracts === 999">
                全策略永久使用 + 自动更新
              </p>
              <p v-else>
                {{ config.features.strategies.contracts }} 合约 + {{ config.features.strategies.spot }} 现货策略
              </p>
            </div>

            <div class="feature-category">
              <h4>🎧 服务支持</h4>
              <p>{{ config.features.service.support }}</p>
              <p>{{ config.features.service.response }}</p>
            </div>

            <div class="feature-category">
              <h4>🎁 专属权益</h4>
              <div class="benefits-list">
                <p v-if="config.features.benefits.discount">{{ config.features.benefits.discount }}</p>
                <p v-if="config.features.benefits.trial">{{ config.features.benefits.trial }}</p>
                <p v-if="config.features.benefits.referral">{{ config.features.benefits.referral }}</p>
              </div>
            </div>
          </div>

          <button
            class="activate-button"
            @click="goToContactService"
          >
            <i class="fas fa-headset"></i>
            联系客服开通
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && membershipConfigs.length === 0" class="loading-section">
        <i class="fas fa-spinner fa-spin"></i>
        <p>正在加载会员信息...</p>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="error-section">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadMembershipConfigs" class="retry-button">
          重试
        </button>
      </div>

      <!-- 当前会员状态 -->
      <div v-if="currentMembership" class="current-membership">
        <h3>当前会员状态</h3>
        <div class="membership-status">
          <div class="status-item">
            <span class="label">会员等级:</span>
            <span class="value">{{ getMembershipLevelName(currentMembership.membershipLevel) }}</span>
          </div>
          <div class="status-item" v-if="currentMembership.membershipLevel !== 'none'">
            <span class="label">剩余天数:</span>
            <span class="value">
              {{ currentMembership.membershipDaysLeft === -1 ? '永久' : currentMembership.membershipDaysLeft + ' 天' }}
            </span>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'MembershipView',
  setup() {
    const router = useRouter()
    const membershipConfigs = ref([])
    const currentMembership = ref(null)
    const loading = ref(false)
    const error = ref('')

    const goBack = () => {
      router.go(-1)
    }

    const goToContactService = () => {
      router.push({ name: 'contact-service' })
    }

    // 获取会员等级名称
    const getMembershipLevelName = (level) => {
      const levelNames = {
        'none': '普通用户',
        'monthly': '月度会员',
        'quarterly': '季度会员',
        'yearly': '年会员',
        'lifetime': '永久会员'
      }
      return levelNames[level] || '未知'
    }

    // 加载会员配置
    const loadMembershipConfigs = async () => {
      try {
        loading.value = true
        error.value = ''

        const response = await axios.get('/api/membership/config')
        if (response.data.success) {
          membershipConfigs.value = response.data.data
        } else {
          error.value = response.data.error || '获取会员配置失败'
        }
      } catch (err) {
        console.error('获取会员配置错误:', err)
        error.value = '网络错误，请检查连接'
      } finally {
        loading.value = false
      }
    }

    // 获取当前会员信息
    const loadCurrentMembership = async () => {
      try {
        const token = localStorage.getItem('token')
        if (!token) return

        const response = await axios.get('/api/membership/info', {
          headers: { Authorization: `Bearer ${token}` }
        })

        if (response.data.success) {
          currentMembership.value = response.data.data
        }
      } catch (err) {
        console.error('获取会员信息错误:', err)
      }
    }

    onMounted(async () => {
      await loadMembershipConfigs()
      await loadCurrentMembership()
    })

    return {
      membershipConfigs,
      currentMembership,
      loading,
      error,
      goBack,
      goToContactService,
      getMembershipLevelName,
      loadMembershipConfigs
    }
  }
}
</script>

<style scoped>
.membership-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #20b2aa 0%, #008b8b 100%);
  padding-bottom: 80px;
}

.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.intro-section {
  margin-bottom: 2rem;
}

.intro-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.intro-card h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.intro-card p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
}

.membership-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.membership-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.membership-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.membership-card.popular {
  border: 2px solid #ffd700;
  transform: scale(1.05);
}

.popular-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-header h3 {
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.price-section {
  margin-bottom: 1rem;
}

.current-price {
  font-size: 2rem;
  font-weight: 700;
  color: #e74c3c;
  margin-bottom: 0.5rem;
}

.original-price {
  font-size: 1rem;
  color: #999;
  text-decoration: line-through;
}

.features-section {
  margin-bottom: 2rem;
}

.feature-category {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(32, 178, 170, 0.1);
  border-radius: 8px;
}

.feature-category h4 {
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.feature-category p {
  color: #666;
  font-size: 0.95rem;
  margin: 0.25rem 0;
  line-height: 1.4;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.benefits-list p {
  position: relative;
  padding-left: 1rem;
}

.benefits-list p:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #20b2aa;
  font-weight: bold;
}

.activate-button {
  width: 100%;
  background: linear-gradient(45deg, #20b2aa, #008b8b);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.activate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(32, 178, 170, 0.3);
}

.activate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-section, .error-section {
  text-align: center;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  margin: 2rem 0;
}

.loading-section i {
  font-size: 2rem;
  color: #20b2aa;
  margin-bottom: 1rem;
}

.error-section i {
  font-size: 2rem;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background: #20b2aa;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

.current-membership {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
}

.current-membership h3 {
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
}

.membership-status {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(32, 178, 170, 0.1);
  border-radius: 8px;
}

.status-item .label {
  color: #666;
  font-weight: 500;
}

.status-item .value {
  color: #333;
  font-weight: 600;
}

@media (max-width: 768px) {
  .membership-cards {
    grid-template-columns: 1fr;
  }

  .membership-card.popular {
    transform: none;
  }

  .main-content {
    padding: 1rem;
  }
}
</style>
