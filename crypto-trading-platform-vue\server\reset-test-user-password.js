const mongoose = require('mongoose');
const User = require('./models/User');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/crypto-trading-platform');
    console.log('✅ MongoDB连接成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }
};

// 重置测试用户密码
const resetTestUserPasswords = async () => {
  try {
    console.log('=== 重置测试用户密码 ===\n');

    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of testEmails) {
      const user = await User.findOne({ email });
      if (user) {
        user.password = 'test123';
        await user.save();
        console.log(`✅ 重置用户密码: ${user.username} (${email})`);
        
        // 验证密码
        const isValid = await user.comparePassword('test123');
        console.log(`   密码验证结果: ${isValid}`);
      } else {
        console.log(`⚠️  用户不存在: ${email}`);
      }
    }

    console.log('\n=== 密码重置完成 ===');

  } catch (error) {
    console.error('❌ 重置密码失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await resetTestUserPasswords();
  
  console.log('\n操作完成');
  process.exit(0);
};

// 运行
main().catch(error => {
  console.error('❌ 操作失败:', error);
  process.exit(1);
});
