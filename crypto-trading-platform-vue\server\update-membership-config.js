const mongoose = require('mongoose');
const MembershipConfig = require('./models/MembershipConfig');

// 更新会员配置数据
const updateMembershipConfigs = async () => {
  try {
    console.log('🚀 开始更新会员配置数据...');

    // 连接数据库
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading';
    await mongoose.connect(mongoURI);
    console.log('✅ 数据库连接成功');

    // 更新的会员配置
    const updatedConfigs = [
      {
        level: 'monthly',
        name: '月会员',
        price: 316.88,
        originalPrice: 516.88,
        duration: 30,
        features: {
          strategies: {
            contracts: 6,
            spot: 3
          },
          service: {
            support: '工作日客服（30分钟响应）',
            response: '基础教程包'
          },
          benefits: {
            discount: '首单9折',
            trial: '月1次策略体验（3天）',
            referral: ''
          }
        },
        isActive: true,
        order: 1
      },
      {
        level: 'quarterly',
        name: '季会员',
        price: 466.88,
        originalPrice: 766.88,
        duration: 90,
        features: {
          strategies: {
            contracts: 8,
            spot: 4
          },
          service: {
            support: '专属客服（15分钟响应）',
            response: '进阶教程包'
          },
          benefits: {
            discount: '8.5折优惠',
            trial: '季2次策略体验（5天）',
            referral: '推荐1人得50元消费券'
          }
        },
        isActive: true,
        order: 2
      },
      {
        level: 'yearly',
        name: '年会员',
        price: 1060.88,
        originalPrice: 1688.88,
        duration: 365,
        features: {
          strategies: {
            contracts: 10,
            spot: 5
          },
          service: {
            support: '7×24小时极速客服',
            response: '每月策略培训'
          },
          benefits: {
            discount: '8折优惠',
            trial: '年4次新策略优先体验（7天）',
            referral: '推荐1人得100元消费券'
          }
        },
        isActive: true,
        order: 3
      },
      {
        level: 'lifetime',
        name: '永久会员',
        price: 3999.99,
        originalPrice: 9999.99,
        duration: -1, // -1表示永久
        features: {
          strategies: {
            contracts: 999,
            spot: 999
          },
          service: {
            support: '7×24专属客户经理',
            response: '季度策略研讨会'
          },
          benefits: {
            discount: '终身版本免费升级',
            trial: '推荐无上限（200元/人）',
            referral: '收益分成权益'
          }
        },
        isActive: true,
        order: 4
      }
    ];

    // 逐个更新配置
    for (const config of updatedConfigs) {
      await MembershipConfig.findOneAndUpdate(
        { level: config.level },
        {
          name: config.name,
          price: config.price,
          originalPrice: config.originalPrice,
          duration: config.duration,
          features: config.features,
          isActive: config.isActive,
          order: config.order,
          updatedAt: new Date()
        },
        { upsert: true, new: true }
      );
      console.log(`✅ 更新 ${config.name} 配置成功`);
    }

    // 验证更新结果
    const verifyConfigs = await MembershipConfig.find().sort({ order: 1 });
    console.log(`\n📋 更新后的会员配置 (${verifyConfigs.length}条):`);
    verifyConfigs.forEach(config => {
      console.log(`- ${config.name}: ¥${config.price} (原价: ¥${config.originalPrice})`);
      console.log(`  策略权限: ${config.features.strategies.contracts}合约 + ${config.features.strategies.spot}现货`);
      console.log(`  服务支持: ${config.features.service.support}`);
      console.log(`  专属权益: ${config.features.benefits.discount}`);
      console.log('');
    });

    console.log('✅ 会员配置更新完成');

  } catch (error) {
    console.error('❌ 更新会员配置失败:', error);
  } finally {
    await mongoose.disconnect();
  }
};

// 运行脚本
if (require.main === module) {
  updateMembershipConfigs().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { updateMembershipConfigs };
