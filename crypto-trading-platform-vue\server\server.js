// 加载环境变量
require('dotenv').config();

// 强制使用MongoDB存储策略数据
process.env.USE_MONGODB = 'true';

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const axios = require('axios');
const Binance = require('node-binance-api');
const mongoose = require('mongoose');
const Strategy = require('./models/Strategy');
const okxService = require('./services/okxService');
const binanceService = require('./services/binanceService');
const pythonTradeService = require('./services/pythonTradeService');
const binancePythonTradeService = require('./services/binancePythonTradeService');
const strategyMonitorService = require('./services/strategyMonitorService');
const binanceStrategyMonitorService = require('./services/binanceStrategyMonitorService');
const backtestService = require('./services/backtestService');
const { router: authRouter, authenticateToken } = require('./routes/auth');
const { router: inviteRouter } = require('./routes/invite');
const { router: earningsRouter } = require('./routes/earnings');
const rankingRouter = require('./routes/ranking');
const membershipRouter = require('./routes/membership');
const userRouter = require('./routes/user');

// 币安API密钥存储路径
const fs = require('fs');
const path = require('path');
const DATA_DIR = path.join(__dirname, 'data');
const BINANCE_API_KEYS_FILE = path.join(DATA_DIR, 'binance_api_keys.json');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// 如果币安API密钥文件不存在，创建一个空的JSON文件
if (!fs.existsSync(BINANCE_API_KEYS_FILE)) {
  fs.writeFileSync(BINANCE_API_KEYS_FILE, JSON.stringify({}), 'utf8');
}

/**
 * 保存币安用户API密钥
 * @param {string} userId 用户ID
 * @param {Object} keys API密钥对象
 */
function saveBinanceApiKeys(userId, keys) {
  try {
    // 读取现有的API密钥
    let apiKeys = {};
    if (fs.existsSync(BINANCE_API_KEYS_FILE)) {
      const fileContent = fs.readFileSync(BINANCE_API_KEYS_FILE, 'utf8');
      if (fileContent.trim()) {
        apiKeys = JSON.parse(fileContent);
      }
    }

    // 更新API密钥，标注为币安
    apiKeys[userId] = {
      ...keys,
      exchange: 'binance',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 写入文件
    fs.writeFileSync(BINANCE_API_KEYS_FILE, JSON.stringify(apiKeys, null, 2), 'utf8');
    console.log(`币安API密钥已保存到文件: ${BINANCE_API_KEYS_FILE}`);
  } catch (error) {
    console.error('保存币安API密钥到文件失败:', error);
  }
}

/**
 * 获取币安用户API密钥
 * @param {string} userId 用户ID
 * @returns {Object|null} API密钥对象
 */
function getBinanceApiKeys(userId) {
  try {
    if (fs.existsSync(BINANCE_API_KEYS_FILE)) {
      const fileContent = fs.readFileSync(BINANCE_API_KEYS_FILE, 'utf8');
      if (fileContent.trim()) {
        const apiKeys = JSON.parse(fileContent);
        const keys = apiKeys[userId];

        // 确保是币安的API密钥
        if (keys && keys.exchange === 'binance') {
          return keys;
        }
      }
    }
  } catch (error) {
    console.error('从文件读取币安API密钥失败:', error);
  }

  return null;
}

// 初始化Express应用
const app = express();
const server = http.createServer(app);

// 配置CORS
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:8080', 'http://localhost:3008', 'http://127.0.0.1:5174'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true
}));
app.use(express.json());

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'public')));

// 添加测试页面路由
app.get('/test-okx', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'test-okx.html'));
});

// 添加Python帮助页面路由
app.get('/python-help', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'python-help.html'));
});

// 初始化Socket.io
const io = socketIo(server, {
  cors: {
    origin: ['http://localhost:5173', 'http://localhost:8080', 'http://localhost:3008'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// 初始化Binance API (仅保留兼容性，价格数据已改用Gate.io API)
const binance = new Binance();

// 导入Gate.io API服务
const gateApi = require('./services/gate-api');

// 认证路由
app.use('/api/auth', authRouter);

// 邀请路由（需要认证）
app.use('/api/invite', authenticateToken, inviteRouter);

// 收益路由（需要认证）
app.use('/api/earnings', authenticateToken, earningsRouter);

// 排行榜路由
app.use('/api/ranking', rankingRouter);

// 会员路由
app.use('/api/membership', membershipRouter);

// 用户管理路由
app.use('/api/user', userRouter);

// 路由
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// 获取加密货币价格 (使用CoinGecko API按市值排序)
app.get('/api/prices', async (req, res) => {
  try {
    console.log('获取CoinGecko市值数据...');

    // 使用CoinGecko API获取按市值排序的加密货币数据
    const prices = await gateApi.getCoinGeckoMarketData(20);

    // 打印前5个币种的信息用于调试
    prices.slice(0, 5).forEach(item => {
      console.log(`币种: ${item.symbol}, 价格: $${item.lastPrice}, 市值: $${item.marketCap.toLocaleString()}, 涨跌幅: ${item.priceChangePercent}%`);
    });

    console.log(`返回 ${prices.length} 个按市值排序的币种数据`);
    res.json(prices);
  } catch (error) {
    console.error('Error fetching market cap data from CoinGecko:', error);

    // 如果CoinGecko API失败，回退到Gate.io API
    try {
      console.log('CoinGecko API失败，回退到Gate.io API...');
      const tickers = await gateApi.getTickers();
      const fallbackPrices = gateApi.formatTickerData(tickers, 20);
      console.log(`回退: 返回 ${fallbackPrices.length} 个Gate.io交易对数据`);
      res.json(fallbackPrices);
    } catch (fallbackError) {
      console.error('Gate.io API也失败:', fallbackError);
      res.status(500).json({ error: 'Failed to fetch cryptocurrency data from both CoinGecko and Gate.io' });
    }
  }
});

// OKX API验证 (需要认证)
app.post('/api/okx/validate', authenticateToken, async (req, res) => {
  try {
    console.log('收到OKX API验证请求:', {
      headers: req.headers,
      body: {
        ...req.body,
        apiKey: req.body.apiKey ? '***' : undefined,
        secretKey: req.body.secretKey ? '***' : undefined,
        passphrase: req.body.passphrase ? '***' : undefined
      }
    });
    const { apiKey, secretKey, passphrase } = req.body;
    const userId = req.user.uid; // 使用认证用户的UID

    if (!apiKey || !secretKey || !passphrase) {
      console.log('验证失败: 缺少必要参数');
      return res.status(400).json({ success: false, error: '缺少必要的API参数' });
    }

    console.log('开始验证OKX API密钥...');

    try {
      const isValid = await okxService.validateApiKeys(apiKey, secretKey, passphrase);
      console.log('验证结果:', isValid);

      if (isValid) {
        // 保存API密钥
        okxService.saveUserApiKeys(userId, { apiKey, secretKey, passphrase });
        console.log('API密钥验证成功并保存，用户UID:', userId);
        return res.json({ success: true, message: 'API密钥验证成功' });
      } else {
        console.log('API密钥验证失败');
        return res.status(401).json({
          success: false,
          error: 'API密钥验证失败，请检查API密钥、Secret Key和Passphrase是否正确，并确保API密钥具有交易权限'
        });
      }
    } catch (validationError) {
      console.error('API验证过程中出错:', validationError);

      // 检查是否是授权错误
      if (validationError.message && validationError.message.includes('401')) {
        return res.status(401).json({
          success: false,
          error: 'API密钥验证失败: 授权错误，请检查API密钥是否有效且具有足够的权限'
        });
      }

      // 检查是否是网络错误
      if (validationError.message && validationError.message.includes('ECONNREFUSED')) {
        return res.status(503).json({
          success: false,
          error: '无法连接到OKX服务器，请检查网络连接'
        });
      }

      // 其他错误
      return res.status(500).json({
        success: false,
        error: `验证过程中发生错误: ${validationError.message}`
      });
    }
  } catch (error) {
    console.error('OKX API验证错误:', error);
    return res.status(500).json({
      success: false,
      error: '验证过程中发生服务器错误，请稍后重试'
    });
  }
});

// 直接保存OKX API密钥（不进行验证，需要认证）
app.post('/api/okx/save-keys', authenticateToken, async (req, res) => {
  try {
    console.log('收到保存OKX API密钥请求:', {
      headers: req.headers,
      body: {
        ...req.body,
        apiKey: req.body.apiKey ? '***' : undefined,
        secretKey: req.body.secretKey ? '***' : undefined,
        passphrase: req.body.passphrase ? '***' : undefined
      }
    });

    const { apiKey, secretKey, passphrase } = req.body;
    const userId = req.user.uid; // 使用认证用户的UID

    if (!apiKey || !secretKey || !passphrase) {
      console.log('保存失败: 缺少必要参数');
      return res.status(400).json({ success: false, error: '缺少必要的API参数' });
    }

    // 直接保存API密钥，不进行验证
    okxService.saveUserApiKeys(userId, { apiKey, secretKey, passphrase });
    console.log('API密钥保存成功，用户UID:', userId);

    return res.json({
      success: true,
      message: 'API密钥已保存，将用于Python SDK交易'
    });
  } catch (error) {
    console.error('保存OKX API密钥错误:', error);
    return res.status(500).json({
      success: false,
      error: '保存过程中发生服务器错误，请稍后重试'
    });
  }
});

// 获取用户的OKX API密钥状态（需要认证）
app.get('/api/okx/api-keys', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('获取API密钥状态请求，用户UID:', userId);

    // 检查用户是否已配置API密钥
    const apiKeys = okxService.getUserApiKeys(userId);

    if (apiKeys && apiKeys.apiKey && apiKeys.secretKey && apiKeys.passphrase) {
      // 返回API密钥已配置的状态，但不返回实际的密钥内容
      return res.json({
        success: true,
        hasApiKeys: true,
        apiKeyPreview: apiKeys.apiKey ? `${apiKeys.apiKey.substring(0, 4)}...${apiKeys.apiKey.substring(apiKeys.apiKey.length - 4)}` : '',
        message: 'API密钥已配置'
      });
    } else {
      return res.json({
        success: true,
        hasApiKeys: false,
        message: '尚未配置API密钥'
      });
    }
  } catch (error) {
    console.error('获取API密钥状态错误:', error);
    return res.status(500).json({
      success: false,
      error: '获取API密钥状态失败'
    });
  }
});

// ==================== 币安 API 路由 ====================

// 保存币安API密钥（需要认证）
app.post('/api/binance/save-keys', authenticateToken, async (req, res) => {
  try {
    console.log('收到保存币安API密钥请求:', {
      headers: req.headers,
      body: {
        ...req.body,
        apiKey: req.body.apiKey ? '***' : undefined,
        secretKey: req.body.secretKey ? '***' : undefined
      }
    });

    const { apiKey, secretKey } = req.body;
    const userId = req.user.uid; // 使用认证用户的UID

    if (!apiKey || !secretKey) {
      console.log('保存失败: 缺少必要参数');
      return res.status(400).json({ success: false, error: '缺少必要的API参数' });
    }

    // 保存币安API密钥到文件，标注为币安
    saveBinanceApiKeys(userId, { apiKey, secretKey });
    console.log('币安API密钥保存成功，用户UID:', userId);

    return res.json({
      success: true,
      message: '币安API密钥已保存'
    });
  } catch (error) {
    console.error('保存币安API密钥错误:', error);
    return res.status(500).json({
      success: false,
      error: '保存过程中发生服务器错误，请稍后重试'
    });
  }
});

// 获取用户的币安API密钥状态（需要认证）
app.get('/api/binance/api-keys', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('获取币安API密钥状态请求，用户UID:', userId);

    // 检查用户是否已配置币安API密钥
    const apiKeys = getBinanceApiKeys(userId);

    if (apiKeys && apiKeys.apiKey && apiKeys.secretKey) {
      // 返回API密钥已配置的状态，但不返回实际的密钥内容
      return res.json({
        success: true,
        hasApiKeys: true,
        apiKeyPreview: apiKeys.apiKey ? `${apiKeys.apiKey.substring(0, 4)}...${apiKeys.apiKey.substring(apiKeys.apiKey.length - 4)}` : '',
        message: '币安API密钥已配置'
      });
    } else {
      return res.json({
        success: true,
        hasApiKeys: false,
        message: '尚未配置币安API密钥'
      });
    }
  } catch (error) {
    console.error('获取币安API密钥状态错误:', error);
    return res.status(500).json({
      success: false,
      error: '获取币安API密钥状态失败'
    });
  }
});

// 创建币安交易策略
app.post('/api/binance/strategy', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    const strategyParams = req.body;
    console.log('创建币安策略请求:', req.body);

    // 验证请求参数
    if (!strategyParams.type || !strategyParams.direction || !strategyParams.amount || !strategyParams.symbol || !strategyParams.strategyTemplate) {
      console.error('请求参数不完整:', strategyParams);
      return res.status(400).json({ error: '请求参数不完整' });
    }

    // 创建策略
    console.log('调用binanceService.createStrategy...');
    const result = await binanceService.createStrategy(userId, strategyParams);
    console.log('binanceService.createStrategy返回结果:', result);

    if (result.error) {
      console.log('创建币安策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('创建币安策略成功');
    res.json(result);
  } catch (error) {
    console.error('创建币安策略错误:', error);
    res.status(500).json({ error: '创建币安策略失败: ' + error.message });
  }
});

// 批量创建币安交易策略
app.post('/api/binance/batch-strategies', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    const { strategies } = req.body;
    console.log('批量创建币安策略请求:', { userId, strategiesCount: strategies?.length });

    // 验证请求参数
    if (!strategies || !Array.isArray(strategies) || strategies.length === 0) {
      console.error('请求参数不完整: 缺少策略数组或策略数组为空');
      return res.status(400).json({ error: '请求参数不完整: 缺少策略数组或策略数组为空' });
    }

    // 验证每个策略的参数
    for (const [index, strategy] of strategies.entries()) {
      if (!strategy.type || !strategy.direction || !strategy.amount || !strategy.symbol || !strategy.strategyTemplate) {
        console.error(`第 ${index + 1} 个币安策略参数不完整:`, strategy);
        return res.status(400).json({ error: `第 ${index + 1} 个币安策略参数不完整` });
      }
    }

    // 批量创建策略
    console.log('调用binanceService.createBatchStrategies...');
    const result = await binanceService.createBatchStrategies(userId, strategies);
    console.log('binanceService.createBatchStrategies返回结果:', result);

    if (result.error) {
      console.log('批量创建币安策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('批量创建币安策略成功');
    res.json(result);
  } catch (error) {
    console.error('批量创建币安策略错误:', error);
    res.status(500).json({ error: '批量创建币安策略失败: ' + error.message });
  }
});

// 获取币安策略列表
app.get('/api/binance/strategies', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid;
    console.log('获取币安策略列表请求，用户UID:', userId);

    const result = await binanceService.getStrategies(userId);

    if (result.success) {
      console.log(`返回 ${result.strategies.length} 个币安策略`);
      res.json(result);
    } else {
      console.log('获取币安策略列表失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('获取币安策略列表错误:', error);
    res.status(500).json({ error: '获取币安策略列表失败: ' + error.message });
  }
});

// 停止币安策略
app.post('/api/binance/strategies/:id/stop', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    console.log('停止币安策略请求:', strategyId);

    const result = await binanceService.stopStrategy(strategyId);

    if (result.success) {
      console.log('停止币安策略成功');
      res.json(result);
    } else {
      console.log('停止币安策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('停止币安策略错误:', error);
    res.status(500).json({ error: '停止币安策略失败: ' + error.message });
  }
});

// 删除币安策略
app.delete('/api/binance/strategies/:id', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    console.log('删除币安策略请求:', strategyId);

    const result = await binanceService.deleteStrategy(strategyId);

    if (result.success) {
      console.log('删除币安策略成功');
      res.json(result);
    } else {
      console.log('删除币安策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('删除币安策略错误:', error);
    res.status(500).json({ error: '删除币安策略失败: ' + error.message });
  }
});

// 币安策略一键平仓
app.post('/api/binance/strategies/:id/close-position', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    const userId = req.user.uid;
    console.log('币安策略一键平仓请求:', { strategyId, userId });

    const result = await binanceService.closePosition(strategyId, userId);

    if (result.success) {
      console.log('币安策略平仓成功');
      res.json(result);
    } else {
      console.log('币安策略平仓失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('币安策略平仓错误:', error);
    res.status(500).json({ error: '币安策略平仓失败: ' + error.message });
  }
});

// 币安策略恢复
app.post('/api/binance/strategies/:id/recover', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    const userId = req.user.uid;
    console.log('币安策略恢复请求:', { strategyId, userId });

    const result = await binanceService.recoverStrategy(strategyId, userId);

    if (result.success) {
      console.log('币安策略恢复成功');
      res.json(result);
    } else {
      console.log('币安策略恢复失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('币安策略恢复错误:', error);
    res.status(500).json({ error: '币安策略恢复失败: ' + error.message });
  }
});

// 获取币安历史订单
app.get('/api/binance/orders', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid;
    const { symbol, limit = 20, page = 1 } = req.query;
    console.log('获取币安历史订单请求:', { userId, symbol, limit, page });

    const result = await binanceService.getOrderHistory(userId, { symbol, limit: parseInt(limit), page: parseInt(page) });

    if (result.success) {
      console.log(`返回 ${result.orders.length} 个币安历史订单`);
      res.json(result);
    } else {
      console.log('获取币安历史订单失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('获取币安历史订单错误:', error);
    res.status(500).json({ error: '获取币安历史订单失败: ' + error.message });
  }
});

// 获取币安账户信息（需要认证）
app.get('/api/binance/account', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('获取币安账户信息请求，用户UID:', userId);

    // 检查用户是否已配置币安API密钥
    const apiKeys = getBinanceApiKeys(userId);
    if (!apiKeys || !apiKeys.apiKey || !apiKeys.secretKey) {
      console.log('币安API密钥未配置');
      return res.status(400).json({
        success: false,
        error: '请先配置币安API密钥'
      });
    }

    // 调用Python脚本获取币安账户信息
    console.log('调用Python脚本获取币安账户余额，用户ID:', userId);
    const result = await binancePythonTradeService.getBinanceAccountBalance(userId);

    if (result.error) {
      console.error('获取币安账户信息失败:', result.error);
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }

    if (result.success && result.balance) {
      // 格式化账户信息以匹配前端期望的格式
      const accountInfo = {
        balances: result.balance,
        canTrade: true,  // 假设有交易权限，实际应该从API获取
        canWithdraw: true, // 假设有提现权限，实际应该从API获取
        canDeposit: true   // 假设有充值权限，实际应该从API获取
      };

      console.log('币安账户信息获取成功');
      return res.json({
        success: true,
        account: accountInfo
      });
    } else {
      console.error('币安账户信息格式异常:', result);
      return res.status(400).json({
        success: false,
        error: '获取账户信息失败'
      });
    }
  } catch (error) {
    console.error('获取币安账户信息错误:', error);
    res.status(500).json({
      success: false,
      error: '获取账户信息失败: ' + (error.message || '未知错误')
    });
  }
});

// 获取OKX账户持仓信息
app.get('/api/okx/account', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    console.log('🔄 获取真实OKX账户信息请求:', userId);

    // 检查API密钥是否已配置
    const userApiKeys = okxService.getUserApiKeys(userId);
    if (!userApiKeys) {
      console.error('❌ API密钥未配置');
      return res.status(400).json({
        error: '请先配置有效的OKX API密钥才能访问真实交易功能'
      });
    }

    const result = await okxService.getAccountPositions(userId);

    if (result.error) {
      console.error('❌ 获取账户信息失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('✅ 获取真实账户信息成功');
    res.json(result);
  } catch (error) {
    console.error('❌ 获取OKX账户信息错误:', error);
    res.status(500).json({ error: '获取账户信息失败: ' + error.message });
  }
});

// 创建OKX交易策略
app.post('/api/okx/strategy', async (req, res) => {
  try {
    const { userId = 'default', ...strategyParams } = req.body;
    console.log('创建策略请求:', req.body);

    // 验证请求参数
    if (!strategyParams.type || !strategyParams.direction || !strategyParams.amount || !strategyParams.symbol || !strategyParams.strategyTemplate) {
      console.error('请求参数不完整:', strategyParams);
      return res.status(400).json({ error: '请求参数不完整' });
    }

    // 创建策略
    console.log('调用okxService.createStrategy...');
    const result = await okxService.createStrategy(userId, strategyParams);
    console.log('okxService.createStrategy返回结果:', result);

    if (result.error) {
      console.log('创建策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('创建策略成功');
    res.json(result);
  } catch (error) {
    console.error('创建OKX策略错误:', error);
    res.status(500).json({ error: '创建策略失败: ' + error.message });
  }
});

// 批量创建OKX交易策略
app.post('/api/okx/batch-strategies', async (req, res) => {
  try {
    const { userId = 'default', strategies } = req.body;
    console.log('批量创建策略请求:', { userId, strategiesCount: strategies?.length });

    // 验证请求参数
    if (!strategies || !Array.isArray(strategies) || strategies.length === 0) {
      console.error('请求参数不完整: 缺少策略数组或策略数组为空');
      return res.status(400).json({ error: '请求参数不完整: 缺少策略数组或策略数组为空' });
    }

    // 验证每个策略的参数
    for (const [index, strategy] of strategies.entries()) {
      if (!strategy.type || !strategy.direction || !strategy.amount || !strategy.symbol || !strategy.strategyTemplate) {
        console.error(`第 ${index + 1} 个策略参数不完整:`, strategy);
        return res.status(400).json({ error: `第 ${index + 1} 个策略参数不完整` });
      }
    }

    // 批量创建策略
    console.log('调用okxService.createBatchStrategies...');
    const result = await okxService.createBatchStrategies(userId, strategies);
    console.log('okxService.createBatchStrategies返回结果:', result);

    if (result.error) {
      console.log('批量创建策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('批量创建策略成功');
    res.json(result);
  } catch (error) {
    console.error('批量创建OKX策略错误:', error);
    res.status(500).json({ error: '批量创建策略失败: ' + error.message });
  }
});

// 检查Python环境
app.get('/api/python/check-env', async (req, res) => {
  try {
    console.log('检查Python环境...');
    const result = await pythonTradeService.checkEnvironment();

    console.log('Python环境检查结果:', result);
    res.json(result);
  } catch (error) {
    console.error('检查Python环境错误:', error);
    res.status(500).json({
      success: false,
      error: '检查Python环境失败: ' + (error.message || error.error || '未知错误')
    });
  }
});

// Python交易API - 获取账户余额
app.get('/api/python/okx/balance', async (req, res) => {
  try {
    console.log('获取OKX账户余额(Python)...');

    // 先检查Python环境
    try {
      const envCheck = await pythonTradeService.checkEnvironment();
      if (!envCheck.success) {
        console.error('Python环境检查失败:', envCheck);
        return res.status(400).json({
          error: 'Python环境检查失败，无法获取账户余额',
          details: envCheck,
          helpUrl: '/python-help'
        });
      }
      console.log('Python环境检查成功');
    } catch (envError) {
      console.error('Python环境检查错误:', envError);
      return res.status(400).json({
        error: '检查Python环境时出错',
        details: envError,
        helpUrl: '/python-help'
      });
    }

    // 检查API密钥是否已设置
    const userId = req.query.userId || 'default';
    const apiKeys = okxService.getUserApiKeys(userId);
    if (!apiKeys) {
      console.error('OKX API密钥未设置，无法获取账户余额');
      return res.status(400).json({
        error: 'API密钥未设置，请先配置API密钥',
        helpUrl: '/test-okx'
      });
    }

    // 获取账户余额
    const result = await pythonTradeService.getAccountBalance();

    if (result.error) {
      console.log('获取账户余额失败:', result.error);
      return res.status(400).json({
        error: result.error,
        helpUrl: '/python-help'
      });
    }

    console.log('获取账户余额成功');
    res.json(result);
  } catch (error) {
    console.error('获取OKX账户余额错误:', error);
    res.status(500).json({
      error: '获取账户余额失败: ' + (error.message || '未知错误'),
      helpUrl: '/python-help'
    });
  }
});

// Python交易API - 获取市场价格
app.get('/api/python/okx/price', async (req, res) => {
  try {
    const symbol = req.query.symbol;
    if (!symbol) {
      return res.status(400).json({ error: '缺少交易对参数' });
    }

    console.log(`获取OKX市场价格(Python): ${symbol}`);
    const result = await pythonTradeService.getMarketPrice(symbol);

    if (result.error) {
      console.log('获取市场价格失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('获取市场价格成功');
    res.json(result);
  } catch (error) {
    console.error('获取OKX市场价格错误:', error);
    res.status(500).json({ error: '获取市场价格失败' });
  }
});

// Python交易API - 执行交易
app.post('/api/python/okx/trade', async (req, res) => {
  try {
    const { symbol, side, amount, price } = req.body;

    if (!symbol || !side || !amount) {
      return res.status(400).json({ error: '缺少必要的交易参数' });
    }

    if (side !== 'buy' && side !== 'sell') {
      return res.status(400).json({ error: '交易方向无效，必须是 buy 或 sell' });
    }

    console.log(`执行OKX交易(Python): ${side} ${amount} ${symbol} @ ${price || 'market'}`);
    const result = await pythonTradeService.executeTrade(symbol, side, parseFloat(amount), price ? parseFloat(price) : undefined);

    if (result.error) {
      console.log('执行交易失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('执行交易成功');
    res.json(result);
  } catch (error) {
    console.error('执行OKX交易错误:', error);
    res.status(500).json({ error: '执行交易失败' });
  }
});

// 获取OKX历史订单
app.get('/api/okx/orders', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    const params = req.query;
    console.log('获取历史订单请求:', params);

    const result = await okxService.getHistoryOrders(userId, params);

    if (result.error) {
      console.log('获取历史订单失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('获取历史订单成功');
    res.json(result);
  } catch (error) {
    console.error('获取OKX历史订单错误:', error);
    res.status(500).json({ error: '获取历史订单失败' });
  }
});

// 获取策略列表
app.get('/api/strategies', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    console.log('获取策略列表请求:', userId);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategies = [];

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          // 如果模型未注册，则加载模型
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取OKX策略列表（只获取OKX策略）
        strategies = await Strategy.find({
          userId,
          $or: [
            { exchange: 'okx' },
            { exchange: { $exists: false } } // 兼容旧数据
          ]
        }).sort({ createdAt: -1 });
        console.log(`MongoDB: 找到 ${strategies.length} 个OKX策略，用户ID: ${userId}`);

        // 打印每个策略的基本信息
        strategies.forEach((strategy, index) => {
          console.log(`策略 ${index + 1}:`, {
            id: strategy._id.toString(),
            name: strategy.strategyName,
            status: strategy.status,
            type: strategy.type,
            createdAt: strategy.createdAt
          });
        });
      } catch (error) {
        console.error('MongoDB查询失败:', error);
      }
    } else {
      // 从内存中获取策略列表
      const strategyMonitorService = require('./services/strategyMonitorService');
      const allStrategies = strategyMonitorService.getInMemoryStrategies();
      console.log(`内存中共有 ${allStrategies.size} 个策略`);

      strategies = Array.from(allStrategies.values())
        .filter(strategy => strategy.userId === userId)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      console.log(`内存: 找到 ${strategies.length} 个策略，用户ID: ${userId}`);

      // 打印每个策略的基本信息
      strategies.forEach((strategy, index) => {
        console.log(`策略 ${index + 1}:`, {
          id: strategy.id || strategy._id,
          name: strategy.strategyName,
          status: strategy.status,
          type: strategy.type,
          createdAt: strategy.createdAt
        });
      });
    }

    // 检查监控任务状态
    const strategyMonitorService = require('./services/strategyMonitorService');
    const monitorJobs = strategyMonitorService.getMonitorJobs();
    console.log(`当前有 ${monitorJobs.size} 个活跃的监控任务`);

    // 打印每个监控任务的ID
    if (monitorJobs.size > 0) {
      console.log('活跃的监控任务ID:', Array.from(monitorJobs.keys()));
    }

    // 为每个策略添加网络状态检查和实时盈利计算
    const strategiesWithStatus = await Promise.all(strategies.map(async strategy => {
      const strategyObj = strategy.toObject ? strategy.toObject() : strategy;

      // 检查最近的日志是否包含网络错误
      let networkStatus = 'ok';
      if (strategyObj.logs && strategyObj.logs.length > 0) {
        const recentLogs = strategyObj.logs.slice(-5); // 检查最近5条日志
        const hasNetworkError = recentLogs.some(log =>
          log.message.includes('网络连接失败') ||
          log.message.includes('获取K线数据失败') ||
          log.message.includes('获取最新价格失败') ||
          log.message.includes('socket disconnected')
        );

        if (hasNetworkError) {
          networkStatus = 'network_error';
        }
      }

      // 为active状态的策略计算实时盈利
      let profit = 0;
      let profitPercentage = 0;
      let currentPrice = strategyObj.currentPrice;

      if (strategyObj.status === 'active' && strategyObj.orders && strategyObj.orders.length > 0) {
        try {
          console.log(`计算策略 ${strategyObj.strategyName} 的实时盈利...`);

          // 获取当前价格 - 优先使用数据库中的最新价格，如果没有则获取实时价格
          if (!currentPrice) {
            console.log(`获取策略 ${strategyObj.strategyName} 的实时价格...`);

            try {
              // 使用marketDataService获取价格，它有重试机制
              const marketDataService = require('./services/marketDataService');
              const okxService = require('./services/okxService');
              const userApiKeys = okxService.getUserApiKeys(strategyObj.userId);

              let credentials;
              if (userApiKeys) {
                credentials = {
                  apiKey: userApiKeys.apiKey,
                  secret: userApiKeys.secretKey,
                  password: userApiKeys.passphrase
                };
                console.log(`使用用户 ${strategyObj.userId} 的API密钥`);
              } else {
                credentials = {
                  apiKey: process.env.OKX_API_KEY,
                  secret: process.env.OKX_SECRET_KEY,
                  password: process.env.OKX_PASSPHRASE
                };
                console.log('使用默认API密钥');
              }

              // 前端API不再重复获取价格，避免与策略监控冲突
              // 策略监控会自动更新数据库中的价格，前端直接使用即可
              console.log(`前端API跳过价格获取，使用数据库中的价格: ${currentPrice}`);

              // 如果数据库中没有价格，才获取一次
              if (!currentPrice || currentPrice === 0) {
                console.log(`数据库中没有价格，获取一次实时价格...`);
                currentPrice = await marketDataService.getLatestPrice(strategyObj.symbol, credentials);
                console.log(`获取到 ${strategyObj.symbol} 实时价格: ${currentPrice}`);

                // 更新数据库中的价格
                if (useMongoDb && strategyObj._id) {
                  await Strategy.findByIdAndUpdate(strategyObj._id, {
                    currentPrice: currentPrice,
                    updatedAt: new Date()
                  });
                  console.log(`已更新数据库中的价格: ${currentPrice}`);
                }
              }

            } catch (priceError) {
              console.error(`获取实时价格失败: ${priceError.message}`);
              // 使用入场价格作为备用
              currentPrice = strategyObj.entryPrice || 0;
              console.log(`使用入场价格作为备用: ${currentPrice}`);
            }
          } else {
            console.log(`使用数据库中的价格: ${currentPrice}`);
          }

          // 计算盈利
          const buyOrders = strategyObj.orders.filter(order => order.side === 'buy');
          if (buyOrders.length > 0) {
            const totalCost = buyOrders.reduce((sum, order) => sum + (Number(order.price) * Number(order.amount)), 0);
            const totalAmount = buyOrders.reduce((sum, order) => sum + Number(order.amount), 0);
            const currentValue = totalAmount * Number(currentPrice);
            profit = Number((currentValue - totalCost).toFixed(2));
            profitPercentage = Number((((currentValue - totalCost) / totalCost) * 100).toFixed(2));

            console.log(`策略 ${strategyObj.strategyName} 前端盈利计算:`, {
              totalCost: totalCost.toFixed(2),
              totalAmount: totalAmount.toFixed(4),
              currentPrice,
              currentValue: currentValue.toFixed(2),
              profit: profit.toFixed(2),
              profitPercentage: profitPercentage.toFixed(2) + '%',
              订单数量: buyOrders.length
            });
            console.log(`前端盈利计算订单:`, buyOrders.map(order => ({
              price: Number(order.price),
              amount: Number(order.amount),
              cost: Number(order.price) * Number(order.amount)
            })));
          }
        } catch (error) {
          console.error(`获取策略 ${strategyObj.strategyName} 实时价格失败:`, error.message);
          // 使用已有的currentPrice或entryPrice
          currentPrice = strategyObj.currentPrice || strategyObj.entryPrice || 0;
          console.log(`使用备用价格: ${currentPrice}`);
        }
      }

      return {
        ...strategyObj,
        networkStatus,
        currentPrice,
        profit,
        profitPercentage
      };
    }));

    res.json({ success: true, strategies: strategiesWithStatus });
  } catch (error) {
    console.error('获取策略列表错误:', error);
    res.status(500).json({ error: '获取策略列表失败' });
  }
});

// 获取单个策略详情
app.get('/api/strategies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('获取策略详情请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);
      } catch (error) {
        console.error('MongoDB查询失败:', error);
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);
    }

    if (!strategy) {
      return res.status(404).json({ error: '策略不存在' });
    }

    console.log('获取策略详情成功');
    res.json({ success: true, strategy });
  } catch (error) {
    console.error('获取策略详情错误:', error);
    res.status(500).json({ error: '获取策略详情失败' });
  }
});

// 停止策略监控
app.post('/api/strategies/:id/stop', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('停止策略监控请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }
      } catch (error) {
        console.error('MongoDB查询失败:', error);
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }
    }

    // 导入策略监控服务
    const strategyMonitorService = require('./services/strategyMonitorService');

    // 检查监控任务状态
    const monitorJobs = strategyMonitorService.getMonitorJobs();
    const hasActiveJob = monitorJobs.has(id);
    console.log(`策略 ${id} 是否有活跃的监控任务: ${hasActiveJob}`);

    // 尝试停止策略监控
    const stopResult = await strategyMonitorService.stopStrategyMonitor(id);
    console.log(`停止策略监控结果: ${stopResult}`);

    // 确保策略状态正确更新
    if (strategy && (strategy.status === 'waiting' || strategy.status === 'active')) {
      console.log(`策略 ${id} 当前状态: ${strategy.status}，更新为completed`);

      if (useMongoDb) {
        // 更新数据库中的策略状态
        try {
          await strategy.updateStatus('completed');
          await strategy.addLog('用户手动停止策略');
          console.log(`策略 ${id} 状态已更新为completed`);
        } catch (updateError) {
          console.error(`更新策略状态失败: ${updateError.message}`);
        }
      } else {
        // 更新内存中的策略状态
        strategy.status = 'completed';
        if (!strategy.logs) strategy.logs = [];
        strategy.logs.push({
          message: '用户手动停止策略',
          timestamp: new Date()
        });
        strategyMonitorService.getInMemoryStrategies().set(id, strategy);
        console.log(`策略 ${id} 状态已更新为completed`);
      }
    } else {
      console.log(`策略 ${id} 当前状态: ${strategy ? strategy.status : '未知'}，无需更新`);
    }

    console.log('停止策略监控成功');
    res.json({ success: true, message: '策略监控已停止' });
  } catch (error) {
    console.error('停止策略监控错误:', error);
    res.status(500).json({ error: '停止策略监控失败' });
  }
});

// 恢复策略监控
app.post('/api/strategies/:id/recover', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.uid;
    console.log('恢复策略监控请求:', { strategyId: id, userId });

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }

        // 验证策略所有者
        if (strategy.userId !== userId) {
          return res.status(403).json({ error: '无权限操作此策略' });
        }

        // 检查策略状态
        if (strategy.status !== 'error') {
          return res.status(400).json({ error: '只能恢复错误状态的策略' });
        }

      } catch (error) {
        console.error('MongoDB查询失败:', error);
        return res.status(500).json({ error: '数据库查询失败' });
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }

      if (strategy.userId !== userId) {
        return res.status(403).json({ error: '无权限操作此策略' });
      }

      if (strategy.status !== 'error') {
        return res.status(400).json({ error: '只能恢复错误状态的策略' });
      }
    }

    // 导入策略监控服务
    const strategyMonitorService = require('./services/strategyMonitorService');

    // 恢复策略状态
    console.log(`恢复策略 ${id}，当前状态: ${strategy.status}`);

    if (useMongoDb) {
      // 更新数据库中的策略状态
      try {
        await strategy.updateStatus('waiting');
        await strategy.addLog('用户手动恢复策略，重新启动监控');
        console.log(`策略 ${id} 状态已更新为waiting`);
      } catch (updateError) {
        console.error(`更新策略状态失败: ${updateError.message}`);
        return res.status(500).json({ error: '恢复策略失败: ' + updateError.message });
      }
    } else {
      // 更新内存中的策略状态
      strategy.status = 'waiting';
      if (!strategy.logs) strategy.logs = [];
      strategy.logs.push({
        message: '用户手动恢复策略，重新启动监控',
        timestamp: new Date()
      });
      strategyMonitorService.getInMemoryStrategies().set(id, strategy);
      console.log(`策略 ${id} 状态已更新为waiting`);
    }

    // 重新启动策略监控
    try {
      await strategyMonitorService.startStrategyMonitor(strategy);
      console.log(`策略 ${id} 监控已重新启动`);
    } catch (monitorError) {
      console.error(`重新启动策略监控失败: ${monitorError.message}`);
      // 如果启动监控失败，将状态改回error
      if (useMongoDb) {
        await strategy.updateStatus('error');
        await strategy.addLog(`恢复失败: ${monitorError.message}`);
      } else {
        strategy.status = 'error';
        strategy.logs.push({
          message: `恢复失败: ${monitorError.message}`,
          timestamp: new Date()
        });
        strategyMonitorService.getInMemoryStrategies().set(id, strategy);
      }
      return res.status(500).json({ error: '重新启动监控失败: ' + monitorError.message });
    }

    console.log('恢复策略监控成功');
    res.json({
      success: true,
      message: '策略已恢复，监控重新启动',
      strategy: {
        id: strategy._id || strategy.id,
        status: strategy.status,
        strategyName: strategy.strategyName
      }
    });
  } catch (error) {
    console.error('恢复策略监控错误:', error);
    res.status(500).json({ error: '恢复策略监控失败: ' + error.message });
  }
});

// 一键平仓
app.post('/api/strategies/:id/close-position', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('一键平仓请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }
      } catch (error) {
        console.error('MongoDB查询失败:', error);
        return res.status(500).json({ error: '数据库查询失败' });
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }
    }

    // 检查策略状态
    if (strategy.status !== 'active') {
      return res.status(400).json({ error: '只有运行中的策略才能执行一键平仓' });
    }

    // 检查是否有持仓
    const hasPosition = strategy.orders && strategy.orders.length > 0 &&
                       strategy.orders.some(order => order.side === 'buy');

    if (!hasPosition) {
      return res.status(400).json({ error: '策略没有持仓，无需平仓' });
    }

    console.log(`策略 ${strategy.strategyName} 开始执行一键平仓`);

    // 获取用户API凭证
    const okxService = require('./services/okxService');
    const userApiKeys = okxService.getUserApiKeys(strategy.userId);

    let credentials;
    if (userApiKeys) {
      credentials = {
        apiKey: userApiKeys.apiKey,
        secret: userApiKeys.secretKey,
        password: userApiKeys.passphrase
      };
    } else {
      credentials = {
        apiKey: process.env.OKX_API_KEY,
        secret: process.env.OKX_SECRET_KEY,
        password: process.env.OKX_PASSPHRASE
      };
    }

    // 执行平仓
    const exitMonitorService = require('./services/exitMonitorService');
    await exitMonitorService.executeExit(strategy, credentials);

    // 停止策略监控
    const strategyMonitorService = require('./services/strategyMonitorService');
    await strategyMonitorService.stopStrategyMonitor(id);

    // 记录日志
    if (useMongoDb && strategy.addLog) {
      await strategy.addLog('用户执行一键平仓');
    } else if (!useMongoDb) {
      if (!strategy.logs) strategy.logs = [];
      strategy.logs.push({
        message: '用户执行一键平仓',
        timestamp: new Date()
      });
      strategyMonitorService.getInMemoryStrategies().set(id, strategy);
    }

    console.log('一键平仓执行成功');
    res.json({ success: true, message: '一键平仓执行成功，所有持仓已卖出' });
  } catch (error) {
    console.error('一键平仓错误:', error);
    res.status(500).json({ error: '一键平仓失败: ' + error.message });
  }
});

// 删除策略
app.delete('/api/strategies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('删除策略请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';

    // 首先尝试停止策略监控（如果有）
    try {
      await strategyMonitorService.stopStrategyMonitor(id);
    } catch (error) {
      console.log(`停止策略监控失败，但将继续删除: ${error.message}`);
    }

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库删除策略
        const result = await Strategy.findByIdAndDelete(id);

        if (!result) {
          console.log(`策略 ${id} 不存在或已被删除`);
        } else {
          console.log(`策略 ${id} 已从数据库中删除`);
        }
      } catch (error) {
        console.error('MongoDB删除失败:', error);
        return res.status(500).json({ error: '删除策略失败: ' + error.message });
      }
    } else {
      // 从内存中删除策略
      const strategies = strategyMonitorService.getInMemoryStrategies();
      if (strategies.has(id)) {
        strategies.delete(id);
        console.log(`策略 ${id} 已从内存中删除`);
      } else {
        console.log(`策略 ${id} 不存在或已被删除`);
      }
    }

    console.log('删除策略成功');
    res.json({ success: true, message: '策略已删除' });
  } catch (error) {
    console.error('删除策略错误:', error);
    res.status(500).json({ error: '删除策略失败' });
  }
});

// 测试OKX API连接
app.get('/api/okx/test-connection', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    console.log('测试OKX API连接:', userId);

    const client = okxService.getOkxClient(userId);
    if (!client) {
      return res.status(400).json({
        success: false,
        error: 'API密钥未设置，请先配置API密钥'
      });
    }

    // 尝试获取系统时间，这是一个简单的公共API调用
    const systemTime = await client.getSystemTime();
    console.log('系统时间:', systemTime);

    // 尝试获取交易对信息，这是一个公共API调用
    const instruments = await client.getInstruments({ instType: 'SPOT' });
    console.log('获取交易对成功，数量:', instruments.length);

    // 尝试获取账户余额，这需要API密钥验证
    try {
      const balance = await client.getBalance();
      console.log('获取账户余额成功');

      // 尝试获取持仓信息
      try {
        const positionsResult = await client.getPositions({ instType: 'ANY' });
        let positions = [];

        // 检查返回结果的格式
        if (positionsResult && Array.isArray(positionsResult)) {
          positions = positionsResult;
        } else if (positionsResult && positionsResult.data && Array.isArray(positionsResult.data)) {
          positions = positionsResult.data;
        } else {
          console.log('获取持仓信息成功，但返回格式异常:', typeof positionsResult);
          positions = [];
        }

        console.log('获取持仓信息成功，持仓数量:', positions.length);

        return res.json({
          success: true,
          message: 'OKX API连接成功',
          systemTime,
          instruments: instruments.slice(0, 3), // 只返回前3个交易对
          balance,
          positions,
          apiVersion: require('okx-api/package.json').version
        });
      } catch (positionsError) {
        console.warn('获取持仓信息失败，可能没有持仓:', positionsError.message);
        return res.json({
          success: true,
          message: 'OKX API连接成功，但获取持仓信息失败',
          systemTime,
          instruments: instruments.slice(0, 3),
          balance,
          positions: [],
          positionsError: positionsError.message,
          apiVersion: require('okx-api/package.json').version
        });
      }
    } catch (balanceError) {
      console.error('获取余额失败:', balanceError);
      return res.json({
        success: true,
        message: 'OKX API公共接口连接成功，但获取账户数据失败',
        systemTime,
        instruments: instruments.slice(0, 3),
        error: balanceError.message,
        apiVersion: require('okx-api/package.json').version
      });
    }
  } catch (error) {
    console.error('测试OKX API连接失败:', error);
    return res.status(500).json({
      success: false,
      error: '测试OKX API连接失败: ' + error.message,
      apiVersion: '2.0.5'
    });
  }
});

// WebSocket连接
io.on('connection', (socket) => {
  console.log('Client connected');

  // 价格更新相关变量
  let priceUpdateFailCount = 0;
  let priceUpdateInterval = 3000; // 初始3秒更新一次

  // 定期发送价格更新 (使用Gate.io API)
  const priceUpdateFunction = async () => {
    try {
      // 使用Gate.io API服务获取价格信息
      const tickers = await gateApi.getTickers();

      // 使用API服务格式化数据
      const prices = gateApi.formatTickerData(tickers, 20);

      // 重置失败计数和更新间隔
      if (priceUpdateFailCount > 0) {
        console.log('Gate.io价格更新恢复正常');
        priceUpdateFailCount = 0;
        priceUpdateInterval = 3000;
      }

      socket.emit('priceUpdate', prices);
    } catch (error) {
      priceUpdateFailCount++;

      // 根据失败次数调整更新频率，避免频繁请求失败的API
      if (priceUpdateFailCount >= 5) {
        if (priceUpdateInterval < 30000) { // 最长延长到30秒
          clearInterval(priceInterval);
          priceUpdateInterval = Math.min(priceUpdateInterval * 2, 30000);
          console.log(`Gate.io价格更新失败${priceUpdateFailCount}次，调整更新间隔为${priceUpdateInterval}ms`);

          // 使用新的间隔重新设置定时器
          priceInterval = setInterval(priceUpdateFunction, priceUpdateInterval);
        }
      }

      console.error(`Gate.io价格更新失败(${priceUpdateFailCount}): ${error.message}`);

      // 发送错误通知给客户端
      if (priceUpdateFailCount % 5 === 0) { // 每5次失败发送一次通知
        socket.emit('priceUpdateError', {
          error: 'Gate.io价格数据暂时不可用，请稍后再试',
          retryIn: Math.floor(priceUpdateInterval / 1000)
        });
      }
    }
  };

  const priceInterval = setInterval(priceUpdateFunction, priceUpdateInterval);

  // OKX WebSocket连接
  socket.on('subscribeOkx', async (data) => {
    const { userId = 'default' } = data;
    console.log('订阅OKX账户更新:', userId);

    // 检查API密钥是否已设置
    const apiKeys = okxService.getUserApiKeys(userId);
    if (!apiKeys) {
      console.error('OKX API密钥未设置，无法订阅账户更新');
      socket.emit('okxError', { error: 'API密钥未设置，请先配置API密钥' });
      return;
    }

    // 订阅OKX账户和持仓更新
    const cleanup = okxService.subscribeAccountUpdates(userId, (updateData) => {
      socket.emit('okxUpdate', updateData);
    });

    // 保存清理函数，在主断开连接处理程序中使用
    socket.okxCleanup = cleanup;

    // 发送初始账户数据
    try {
      console.log('获取OKX初始账户数据...');
      const accountData = await okxService.getAccountPositions(userId);

      if (accountData.error) {
        console.error('获取OKX账户数据失败:', accountData.error);
        socket.emit('okxError', { error: accountData.error });
      } else {
        console.log('成功获取OKX账户数据');
        socket.emit('okxAccountInit', accountData);
      }
    } catch (error) {
      console.error('获取OKX初始账户数据错误:', error);
      socket.emit('okxError', {
        error: '获取账户数据失败',
        details: error.message || '未知错误'
      });
    }
  });

  // 币安WebSocket连接
  socket.on('subscribeBinance', async (data) => {
    const { userId = 'default' } = data;
    console.log('订阅币安账户更新:', userId);

    // 检查API密钥是否已设置
    const apiKeys = getBinanceApiKeys(userId);
    if (!apiKeys || !apiKeys.apiKey || !apiKeys.secretKey) {
      console.error('币安API密钥未设置，无法订阅账户更新');
      socket.emit('binanceError', { error: 'API密钥未设置，请先配置API密钥' });
      return;
    }

    // 发送初始账户数据
    try {
      console.log('获取币安初始账户数据...');
      const accountData = await binancePythonTradeService.getBinanceAccountBalance();

      if (accountData.error) {
        console.error('获取币安账户数据失败:', accountData.error);
        socket.emit('binanceError', { error: accountData.error });
      } else {
        console.log('成功获取币安账户数据');
        socket.emit('binanceAccountInit', accountData);
      }
    } catch (error) {
      console.error('获取币安初始账户数据错误:', error);
      socket.emit('binanceError', {
        error: '获取账户数据失败',
        details: error.message || '未知错误'
      });
    }
  });

  // 断开连接时清除定时器和WebSocket订阅
  socket.on('disconnect', () => {
    console.log('Client disconnected');

    // 清除价格更新定时器
    clearInterval(priceInterval);

    // 清理OKX WebSocket订阅
    try {
      if (socket.okxCleanup && typeof socket.okxCleanup === 'function') {
        console.log('清理OKX WebSocket订阅');
        socket.okxCleanup();
      }
    } catch (error) {
      console.error('清理OKX WebSocket订阅失败:', error);
    }
  });
});

// 连接MongoDB数据库
const connectDB = async () => {
  try {
    console.log('正在连接MongoDB数据库...');

    // 使用本地身份验证连接MongoDB
    // 注意：这里使用您系统的用户名和密码
    // 如果您的MongoDB没有设置身份验证，可以使用简单的连接字符串
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    console.log('MongoDB URI:', mongoURI);

    const mongooseOptions = {
      serverSelectionTimeoutMS: 10000 // 10秒超时
    };

    // 尝试连接
    await mongoose.connect(mongoURI, mongooseOptions);

    console.log('MongoDB数据库连接成功');

    // 加载数据模型（不删除缓存，避免影响其他服务）
    require('./models/User');
    require('./models/Strategy');
    console.log('数据模型加载完成');

    // 测试数据库连接
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('数据库集合列表:', collections.map(c => c.name));

    // 设置环境变量
    process.env.USE_MONGODB = 'true';

    return true;
  } catch (error) {
    console.error('MongoDB数据库连接失败:', error);
    console.log('将使用内存存储策略');

    // 设置环境变量
    process.env.USE_MONGODB = 'false';

    return false;
  }
};

// 启动服务器
const PORT = process.env.PORT || 3009; // 使用3009端口
server.listen(PORT, async () => {
  console.log(`服务器运行在端口 ${PORT}`);

  // 连接数据库
  const dbConnected = await connectDB();

  // 无论数据库是否连接成功，都初始化策略监控服务
  try {
    console.log('正在初始化OKX策略监控服务...');
    await strategyMonitorService.initStrategyMonitor();
    console.log('OKX策略监控服务初始化成功');
  } catch (error) {
    console.error('OKX策略监控服务初始化失败:', error);
  }

  // 初始化币安策略监控服务
  try {
    console.log('正在初始化币安策略监控服务...');
    await binanceStrategyMonitorService.initStrategyMonitor();
    console.log('币安策略监控服务初始化成功');
  } catch (error) {
    console.error('币安策略监控服务初始化失败:', error);
  }
});

// ==================== 策略回测 API 路由 ====================

// 运行策略回测（需要认证）
app.post('/api/backtest/run', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid;
    const {
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate,
      strategyParams
    } = req.body;

    console.log('收到回测请求:', {
      userId,
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital
    });

    // 验证请求参数
    if (!exchange || !strategyId || !symbol || !timeframe || !startDate || !endDate || !initialCapital) {
      return res.status(400).json({
        success: false,
        error: '缺少必要的回测参数'
      });
    }

    // 验证交易所
    if (!['binance', 'okx'].includes(exchange)) {
      return res.status(400).json({
        success: false,
        error: '不支持的交易所'
      });
    }

    // 验证日期
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (start >= end) {
      return res.status(400).json({
        success: false,
        error: '开始日期必须早于结束日期'
      });
    }

    // 验证初始资金
    if (initialCapital <= 0) {
      return res.status(400).json({
        success: false,
        error: '初始资金必须大于0'
      });
    }

    // 运行回测
    const result = await backtestService.runBacktest({
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate: feeRate || 0.1,
      strategyParams: strategyParams || {},
      userId
    });

    if (result.success) {
      console.log('回测完成:', {
        totalReturn: result.results.totalReturn,
        winRate: result.results.winRate,
        totalTrades: result.results.totalTrades
      });
      res.json(result);
    } else {
      console.log('回测失败:', result.error);
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('回测API错误:', error);
    res.status(500).json({
      success: false,
      error: '回测过程中发生服务器错误: ' + error.message
    });
  }
});