<template>
  <div class="user-view">
    <div class="user-header">
      <i class="fas fa-arrow-left back-button" @click="goBack"></i>
      <div class="text-center">会员中心</div>
      <i class="fas fa-ellipsis-v menu-button"></i>

      <div class="user-profile">
        <div class="avatar">
          <i class="fas fa-user"></i>
        </div>
        <div class="user-info">
          <div class="username">{{ currentUser?.username || 'Guest' }}</div>
          <div class="user-id">UID: {{ currentUser?.uid || 'N/A' }}</div>
        </div>
        <div class="upgrade-button" @click="goToMembership">
          <i class="fas fa-crown"></i> 开通会员
        </div>
      </div>

      <div class="user-actions">
        <div class="user-action" v-for="(action, index) in userActions" :key="index" @click="handleAction(action.action)">
          <i :class="action.icon"></i>
          <span>{{ action.label }}</span>
          <span v-if="action.value !== undefined">{{ action.value }}</span>
        </div>
      </div>
    </div>

    <div class="menu-list">
      <!-- 管理员控制面板 -->
      <div v-if="currentUser?.isAdmin" class="admin-panel">
        <div class="admin-header">
          <i class="fas fa-crown"></i>
          <span>管理员控制面板</span>
        </div>
        <div class="admin-actions">
          <div class="admin-action-item" @click="openRankingManagement">
            <div class="admin-action-left">
              <i class="fas fa-trophy"></i>
              <span>排行榜管理</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="admin-action-item" @click="openMembershipManagement">
            <div class="admin-action-left">
              <i class="fas fa-crown"></i>
              <span>会员管理</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="admin-action-item" @click="openUserManagement">
            <div class="admin-action-left">
              <i class="fas fa-users"></i>
              <span>用户管理</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="admin-action-item" @click="openSystemSettings">
            <div class="admin-action-left">
              <i class="fas fa-cogs"></i>
              <span>系统设置</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="admin-action-item" @click="openDataAnalytics">
            <div class="admin-action-left">
              <i class="fas fa-chart-bar"></i>
              <span>数据统计</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="admin-action-item" @click="openContentManagement">
            <div class="admin-action-left">
              <i class="fas fa-edit"></i>
              <span>内容管理</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>

      <div class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="navigateTo(item.route)">
        <div class="menu-item-left">
          <div class="menu-icon" :style="{ backgroundColor: item.color }">
            <i :class="item.icon"></i>
          </div>
          <div class="menu-title">{{ item.title }}</div>
        </div>
        <i class="fas fa-chevron-right menu-arrow"></i>
      </div>
    </div>

    <!-- 会员信息弹窗 -->
    <div v-if="showMembershipModal" class="modal-overlay" @click="closeMembershipModal">
      <div class="modal-content membership-modal" @click.stop>
        <div class="modal-header">
          <h3>
            <i class="fas fa-crown"></i>
            会员信息详情
          </h3>
          <button class="close-button" @click="closeMembershipModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="membership-info">
            <div class="info-card">
              <div class="info-item">
                <label>会员等级</label>
                <span class="membership-level" :class="getMembershipClass(currentUser?.membershipLevel)">
                  {{ getMembershipLevelName(currentUser?.membershipLevel) }}
                </span>
              </div>
              <div class="info-item">
                <label>会员状态</label>
                <span class="membership-status" :class="currentUser?.isValidMember ? 'valid' : 'invalid'">
                  {{ currentUser?.isValidMember ? '有效' : '无效/已过期' }}
                </span>
              </div>
              <div class="info-item">
                <label>剩余天数</label>
                <span class="membership-days">
                  {{ getMembershipDaysText(currentUser) }}
                </span>
              </div>
              <div v-if="currentUser?.membershipStartDate" class="info-item">
                <label>开始时间</label>
                <span>{{ formatDate(currentUser.membershipStartDate) }}</span>
              </div>
              <div v-if="currentUser?.membershipExpiry || currentUser?.membershipLevel === 'lifetime'" class="info-item">
                <label>到期时间</label>
                <span>
                  {{ currentUser?.membershipLevel === 'lifetime' ? '永不过期' : formatDate(currentUser.membershipExpiry) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="close-modal-button" @click="closeMembershipModal">关闭</button>
          <button class="upgrade-button" @click="goToMembership">
            <i class="fas fa-arrow-up"></i>
            升级会员
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'UserView',
  setup() {
    const router = useRouter()

    // 获取当前用户信息
    const currentUser = ref(null)

    // 定时器引用
    let membershipTimer = null

    // 会员信息弹窗
    const showMembershipModal = ref(false)

    // 加载用户信息
    const loadUserInfo = async () => {
      const userStr = localStorage.getItem('user')
      currentUser.value = userStr ? JSON.parse(userStr) : null
      await updateMembershipDays()
    }

    const userActions = ref([
      { label: '联系客服', icon: 'fas fa-headset', action: 'contact' },
      { label: '修改密码', icon: 'fas fa-lock', action: 'change-password' },
      { label: '剩余天数', icon: 'fas fa-coins', action: 'days', value: '0' },
      { label: '退出登录', icon: 'fas fa-sign-out-alt', action: 'logout' }
    ])

    // 更新会员剩余天数
    const updateMembershipDays = async () => {
      if (currentUser.value) {
        try {
          // 实时获取最新的会员信息
          const token = localStorage.getItem('token')
          if (token) {
            const response = await axios.get('/api/membership/info', {
              headers: { Authorization: `Bearer ${token}` }
            })

            if (response.data.success) {
              const membershipInfo = response.data.data
              const daysLeft = membershipInfo.membershipDaysLeft || 0
              const daysAction = userActions.value.find(action => action.action === 'days')

              if (daysAction) {
                if (daysLeft === -1) {
                  daysAction.value = '永久'
                } else {
                  daysAction.value = daysLeft.toString()
                }
              }

              // 更新当前用户的会员信息
              if (currentUser.value) {
                currentUser.value.membershipLevel = membershipInfo.membershipLevel
                currentUser.value.membershipExpiry = membershipInfo.membershipExpiry
                currentUser.value.membershipStartDate = membershipInfo.membershipStartDate
                currentUser.value.membershipDaysLeft = membershipInfo.membershipDaysLeft
                currentUser.value.isValidMember = membershipInfo.isValidMember

                // 更新localStorage中的用户信息
                localStorage.setItem('user', JSON.stringify(currentUser.value))
              }
            }
          } else {
            // 如果没有token，使用本地存储的信息
            const daysLeft = currentUser.value.membershipDaysLeft || 0
            const daysAction = userActions.value.find(action => action.action === 'days')
            if (daysAction) {
              if (daysLeft === -1) {
                daysAction.value = '永久'
              } else {
                daysAction.value = daysLeft.toString()
              }
            }
          }
        } catch (error) {
          console.error('获取会员信息失败:', error)
          // 如果API调用失败，使用本地存储的信息
          const daysLeft = currentUser.value.membershipDaysLeft || 0
          const daysAction = userActions.value.find(action => action.action === 'days')
          if (daysAction) {
            if (daysLeft === -1) {
              daysAction.value = '永久'
            } else {
              daysAction.value = daysLeft.toString()
            }
          }
        }
      }
    }

    const menuItems = ref([
      {
        title: '常见问题',
        icon: 'fas fa-question-circle',
        color: '#9c6ade',
        route: 'faq'
      },
      {
        title: '策略回测',
        icon: 'fas fa-chart-line',
        color: '#5cd9a6',
        route: 'strategy-backtest'
      },
      {
        title: '邀请好友',
        icon: 'fas fa-user-friends',
        color: '#ff9f43',
        route: 'invite-friends'
      },
      {
        title: '新手指南',
        icon: 'fas fa-lightbulb',
        color: '#feca57',
        route: 'beginner-guide'
      }
    ])

    const goBack = () => {
      router.go(-1)
    }

    const goToMembership = () => {
      router.push({ name: 'membership' })
    }

    const handleAction = async (action) => {
      switch (action) {
        case 'contact':
          // 跳转到联系客服页面
          router.push({ name: 'contact-service' })
          break
        case 'change-password':
          // 跳转到修改密码页面
          router.push({ name: 'change-password' })
          break
        case 'logout':
          // 处理登出逻辑
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          router.push({ name: 'login' })
          break
        case 'days':
          // 处理剩余天数点击
          await showMembershipInfo()
          break
        default:
          alert('该功能正在开发中...')
      }
    }

    const navigateTo = (route) => {
      // 已实现的功能
      if (route === 'invite-friends') {
        router.push({ name: 'invite-friends' })
      } else if (route === 'beginner-guide') {
        router.push({ name: 'beginner-guide' })
      } else if (route === 'faq') {
        router.push({ name: 'faq' })
      } else if (route === 'strategy-backtest') {
        router.push({ name: 'strategy-backtest' })
      } else {
        // 其他路由暂未实现，显示提示
        alert('该功能正在开发中...')
      }
    }

    const openRankingManagement = () => {
      router.push({ name: 'ranking-management' })
    }

    const openMembershipManagement = () => {
      router.push({ name: 'membership-management' })
    }

    const openUserManagement = () => {
      router.push({ name: 'user-management' })
    }

    const openSystemSettings = () => {
      // TODO: 实现系统设置页面
      alert('系统设置功能正在开发中...')
    }

    const openDataAnalytics = () => {
      // TODO: 实现数据统计页面
      alert('数据统计功能正在开发中...')
    }

    const openContentManagement = () => {
      // TODO: 实现内容管理页面
      alert('内容管理功能正在开发中...')
    }

    // 刷新用户信息
    const refreshUserInfo = async () => {
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          loadUserInfo()
          return
        }

        const response = await axios.get('/api/auth/me', {
          headers: { Authorization: `Bearer ${token}` }
        })

        if (response.data.success) {
          let userData = response.data.user
          // 临时修复：如果是管理员邮箱但isAdmin为false，强制设置为true
          if (userData.email === '<EMAIL>' && !userData.isAdmin) {
            console.log('检测到管理员邮箱，强制设置管理员权限')
            userData.isAdmin = true
          }
          localStorage.setItem('user', JSON.stringify(userData))
          currentUser.value = userData
          await updateMembershipDays()
          console.log('用户信息已刷新:', userData)
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error)
        loadUserInfo()
      }
    }

    // 启动会员信息定时刷新
    const startMembershipTimer = () => {
      // 清除现有定时器
      if (membershipTimer) {
        clearInterval(membershipTimer)
      }

      // 每5分钟刷新一次会员信息
      membershipTimer = setInterval(async () => {
        console.log('定时刷新会员信息...')
        await updateMembershipDays()
      }, 5 * 60 * 1000) // 5分钟
    }

    // 停止会员信息定时刷新
    const stopMembershipTimer = () => {
      if (membershipTimer) {
        clearInterval(membershipTimer)
        membershipTimer = null
      }
    }

    // 显示会员信息详情
    const showMembershipInfo = async () => {
      try {
        // 先刷新最新的会员信息
        await updateMembershipDays()

        if (!currentUser.value) {
          alert('请先登录')
          return
        }

        // 显示弹窗
        showMembershipModal.value = true
      } catch (error) {
        console.error('获取会员信息失败:', error)
        alert('获取会员信息失败，请稍后重试')
      }
    }

    // 关闭会员信息弹窗
    const closeMembershipModal = () => {
      showMembershipModal.value = false
    }

    // 获取会员等级名称
    const getMembershipLevelName = (level) => {
      const levelNames = {
        'none': '无会员',
        'monthly': '月会员',
        'quarterly': '季会员',
        'yearly': '年会员',
        'lifetime': '永久会员'
      }
      return levelNames[level] || '未知'
    }

    // 获取会员等级样式类
    const getMembershipClass = (level) => {
      return `membership-${level}`
    }

    // 获取会员剩余天数文本
    const getMembershipDaysText = (user) => {
      if (!user) return '0'
      if (user.membershipLevel === 'none') return '无会员'
      if (user.membershipLevel === 'lifetime') return '永久有效'
      return `${user.membershipDaysLeft || 0} 天`
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }

    // 页面加载时刷新用户信息
    onMounted(async () => {
      await loadUserInfo()
      await refreshUserInfo()

      // 启动定时刷新
      startMembershipTimer()

      // 如果刷新后仍然不是管理员，但邮箱是************，强制设置
      if (currentUser.value?.email === '<EMAIL>' && !currentUser.value?.isAdmin) {
        console.log('检测到管理员邮箱但权限不正确，尝试强制刷新...')
        // 等待一秒后再次尝试
        setTimeout(async () => {
          await refreshUserInfo()
        }, 1000)
      }
    })

    // 页面卸载时清理定时器
    onUnmounted(() => {
      stopMembershipTimer()
    })

    return {
      currentUser,
      userActions,
      menuItems,
      showMembershipModal,
      goBack,
      goToMembership,
      handleAction,
      navigateTo,
      openRankingManagement,
      openMembershipManagement,
      openUserManagement,
      openSystemSettings,
      openDataAnalytics,
      openContentManagement,
      showMembershipInfo,
      closeMembershipModal,
      getMembershipLevelName,
      getMembershipClass,
      getMembershipDaysText,
      formatDate
    }
  }
}
</script>

<style scoped>
.text-center {
  text-align: center;
}

/* 管理员控制面板样式 */
.admin-panel {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  color: white;
  box-shadow: 0 4px 12px rgba(10, 110, 106, 0.3);
}

.admin-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 12px;
}

.admin-header i {
  color: #ffd700;
  font-size: 1.1rem;
}

.admin-actions {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.admin-action-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.admin-action-item:last-child {
  margin-bottom: 0;
}

.admin-action-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(3px);
}

.admin-action-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-action-left i {
  color: #ffd700;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.admin-action-left span {
  font-size: 1rem;
  font-weight: 500;
}

.admin-action-item > i:last-child {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* 会员信息弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.membership-modal {
  max-width: 450px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  border-bottom: 1px solid #eee;
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8078 100%);
  color: white;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-header h3 i {
  color: #ffd700;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.modal-body {
  padding: 25px;
}

.membership-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #666;
  font-size: 0.95rem;
}

.info-item span {
  color: #333;
  font-weight: 500;
  font-size: 0.95rem;
}

.membership-level {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: bold;
  text-align: center;
}

.membership-none {
  background: rgba(128, 128, 128, 0.2);
  color: #666;
}

.membership-monthly {
  background: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.membership-quarterly {
  background: rgba(155, 89, 182, 0.2);
  color: #9b59b6;
}

.membership-yearly {
  background: rgba(230, 126, 34, 0.2);
  color: #e67e22;
}

.membership-lifetime {
  background: rgba(241, 196, 15, 0.2);
  color: #f1c40f;
}

.membership-status.valid {
  color: #27ae60;
  font-weight: bold;
}

.membership-status.invalid {
  color: #e74c3c;
  font-weight: bold;
}

.membership-days {
  color: #2c3e50;
  font-weight: bold;
}

.modal-footer {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 20px 25px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

.close-modal-button,
.upgrade-button {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-modal-button {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
}

.close-modal-button:hover {
  background: #e9ecef;
}

.upgrade-button {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #333;
  font-weight: bold;
}

.upgrade-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-content {
    margin: 10px;
    max-width: none;
  }

  .modal-footer {
    flex-direction: column;
  }

  .close-modal-button,
  .upgrade-button {
    width: 100%;
    justify-content: center;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .info-item span {
    align-self: flex-end;
  }
}
</style>
