const mongoose = require('mongoose');
const User = require('./models/User');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/crypto-trading-platform', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ MongoDB连接成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }
};

// 测试用户管理功能
const testUserManagement = async () => {
  try {
    console.log('\n=== 测试用户管理功能 ===');

    // 1. 获取所有用户
    console.log('\n1. 获取用户列表...');
    const users = await User.find({}).select('-password').limit(5);
    console.log(`找到 ${users.length} 个用户:`);
    
    users.forEach(user => {
      console.log(`- ${user.username} (${user.email}) - 会员等级: ${user.membershipLevel}`);
    });

    if (users.length === 0) {
      console.log('❌ 没有找到用户，请先创建一些测试用户');
      return;
    }

    // 2. 测试会员等级更新
    const testUser = users[0];
    console.log(`\n2. 测试更新用户会员等级 - 用户: ${testUser.username}`);
    console.log(`当前会员等级: ${testUser.membershipLevel}`);
    
    // 更新为月会员
    testUser.membershipLevel = 'monthly';
    testUser.membershipStartDate = new Date();
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30);
    testUser.membershipExpiry = expiryDate;
    
    await testUser.save();
    console.log(`✅ 会员等级已更新为: ${testUser.membershipLevel}`);
    console.log(`到期时间: ${testUser.membershipExpiry}`);
    console.log(`剩余天数: ${testUser.getMembershipDaysLeft()}`);

    // 3. 测试用户状态更新
    console.log(`\n3. 测试更新用户状态 - 用户: ${testUser.username}`);
    console.log(`当前状态 - 激活: ${testUser.isActive}, 管理员: ${testUser.isAdmin}`);
    
    const originalIsActive = testUser.isActive;
    testUser.isActive = !testUser.isActive;
    await testUser.save();
    console.log(`✅ 用户激活状态已更新为: ${testUser.isActive}`);
    
    // 恢复原状态
    testUser.isActive = originalIsActive;
    await testUser.save();
    console.log(`✅ 用户状态已恢复为: ${testUser.isActive}`);

    // 4. 测试搜索功能
    console.log(`\n4. 测试用户搜索功能...`);
    const searchResults = await User.find({
      $or: [
        { username: { $regex: testUser.username.substring(0, 3), $options: 'i' } },
        { email: { $regex: testUser.email.substring(0, 3), $options: 'i' } }
      ]
    }).select('-password').limit(3);
    
    console.log(`搜索结果 (${searchResults.length} 个用户):`);
    searchResults.forEach(user => {
      console.log(`- ${user.username} (${user.email})`);
    });

    // 5. 测试分页功能
    console.log(`\n5. 测试分页功能...`);
    const totalUsers = await User.countDocuments();
    const pageSize = 2;
    const totalPages = Math.ceil(totalUsers / pageSize);
    
    console.log(`总用户数: ${totalUsers}, 每页: ${pageSize}, 总页数: ${totalPages}`);
    
    for (let page = 1; page <= Math.min(totalPages, 3); page++) {
      const skip = (page - 1) * pageSize;
      const pageUsers = await User.find({})
        .select('username email membershipLevel')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(pageSize);
      
      console.log(`第 ${page} 页 (${pageUsers.length} 个用户):`);
      pageUsers.forEach(user => {
        console.log(`  - ${user.username} (${user.email}) - ${user.membershipLevel}`);
      });
    }

    console.log('\n✅ 用户管理功能测试完成');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await testUserManagement();
  
  console.log('\n=== 测试完成 ===');
  process.exit(0);
};

// 运行测试
main().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
