// 完整测试用户管理功能，包括登录认证
const express = require('express');
const mongoose = require('mongoose');
const userRouter = require('./routes/user');
const { router: authRouter, authenticateToken } = require('./routes/auth');
const User = require('./models/User');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/crypto-trading-platform');
    console.log('✅ MongoDB连接成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }
};

// 测试用户管理功能
const testUserManagement = async () => {
  try {
    await connectDB();

    console.log('=== 完整测试用户管理功能 ===\n');

    // 创建Express应用
    const app = express();
    app.use(express.json());

    // 注册路由
    app.use('/api/auth', authRouter);
    app.use('/api/user', userRouter);

    // 启动测试服务器
    const server = app.listen(3010, () => {
      console.log('测试服务器启动在端口 3010');
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 1000));

    const axios = require('axios');
    const AUTH_URL = 'http://localhost:3010/api/auth';
    const USER_URL = 'http://localhost:3010/api/user';

    // 1. 首先登录获取token
    console.log('1. 管理员登录...');
    const loginResponse = await axios.post(`${AUTH_URL}/login`, {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 登录失败:', loginResponse.data.error);
      server.close();
      return;
    }

    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ 登录成功，获取到token');

    // 2. 测试获取用户列表
    console.log('\n2. 测试获取用户列表...');
    try {
      const response = await axios.get(`${USER_URL}/admin/users`, { headers });
      console.log('✅ 获取用户列表成功');
      console.log(`用户数量: ${response.data.data.users.length}`);
      response.data.data.users.forEach(user => {
        console.log(`  - ${user.username} (${user.email}) - 会员: ${user.membershipLevel}`);
      });
    } catch (error) {
      console.error('❌ 获取用户列表失败:', error.response?.data || error.message);
    }

    // 3. 测试搜索用户
    console.log('\n3. 测试搜索用户...');
    try {
      const response = await axios.get(`${USER_URL}/admin/users?search=admin`, { headers });
      console.log('✅ 搜索用户成功');
      console.log(`搜索结果: ${response.data.data.users.length} 个用户`);
    } catch (error) {
      console.error('❌ 搜索用户失败:', error.response?.data || error.message);
    }

    // 4. 测试获取用户详情
    console.log('\n4. 测试获取用户详情...');
    try {
      const usersResponse = await axios.get(`${USER_URL}/admin/users`, { headers });
      const testUser = usersResponse.data.data.users.find(u => u.email !== '<EMAIL>');

      if (testUser) {
        const response = await axios.get(`${USER_URL}/admin/users/${testUser.id}`, { headers });
        console.log('✅ 获取用户详情成功');
        console.log(`用户: ${response.data.data.username} (${response.data.data.email})`);

        // 5. 测试更新会员等级
        console.log('\n5. 测试更新会员等级...');
        const updateResponse = await axios.put(`${USER_URL}/admin/users/${testUser.id}/membership`, {
          membershipLevel: 'yearly',
          duration: 365
        }, { headers });
        console.log('✅ 更新会员等级成功');
        console.log(`新会员等级: ${updateResponse.data.data.membershipLevel}`);

        // 6. 测试更新用户状态
        console.log('\n6. 测试更新用户状态...');
        const statusResponse = await axios.put(`${USER_URL}/admin/users/${testUser.id}/status`, {
          isActive: true,
          isAdmin: false
        }, { headers });
        console.log('✅ 更新用户状态成功');
        console.log(`激活状态: ${statusResponse.data.data.isActive}`);

        // 7. 测试删除用户功能（安全检查）
        console.log('\n7. 测试删除用户功能...');

        // 首先测试删除管理员用户（应该失败）
        const adminUser = usersResponse.data.data.users.find(u => u.isAdmin);
        if (adminUser) {
          try {
            await axios.delete(`${USER_URL}/admin/users/${adminUser.id}`, { headers });
            console.error('❌ 安全检查失败：不应该允许删除管理员用户');
          } catch (error) {
            if (error.response?.status === 400) {
              console.log('✅ 安全检查通过：正确拒绝删除管理员用户');
            } else {
              console.error('❌ 删除管理员用户测试异常:', error.response?.data || error.message);
            }
          }
        }

        // 测试删除普通用户（如果有的话）
        const regularUser = usersResponse.data.data.users.find(u => !u.isAdmin && u.email !== '<EMAIL>');
        if (regularUser) {
          console.log(`\n测试删除普通用户: ${regularUser.username}...`);
          try {
            const deleteResponse = await axios.delete(`${USER_URL}/admin/users/${regularUser.id}`, { headers });
            console.log('✅ 删除普通用户成功');
            console.log(`删除的用户: ${deleteResponse.data.data.deletedUser.username}`);

            // 验证用户确实被删除了
            try {
              await axios.get(`${USER_URL}/admin/users/${regularUser.id}`, { headers });
              console.error('❌ 删除验证失败：用户仍然存在');
            } catch (error) {
              if (error.response?.status === 404) {
                console.log('✅ 删除验证通过：用户已被成功删除');
              } else {
                console.error('❌ 删除验证异常:', error.response?.data || error.message);
              }
            }
          } catch (error) {
            console.error('❌ 删除普通用户失败:', error.response?.data || error.message);
          }
        } else {
          console.log('⚠️  没有找到可删除的普通用户');
        }
      } else {
        console.log('⚠️  没有找到测试用户');
      }
    } catch (error) {
      console.error('❌ 测试失败:', error.response?.data || error.message);
    }

    console.log('\n✅ 用户管理功能测试完成');

    // 关闭服务器
    server.close();
    process.exit(0);

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    process.exit(1);
  }
};

// 运行测试
testUserManagement();
