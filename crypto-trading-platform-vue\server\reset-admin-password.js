const mongoose = require('mongoose');
const User = require('./models/User');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/crypto-trading-platform', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB连接成功');
  } catch (error) {
    console.error('MongoDB连接失败:', error);
    process.exit(1);
  }
};

// 重置管理员密码
const resetAdminPassword = async () => {
  try {
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('❌ 管理员用户不存在');
      return;
    }

    console.log('找到管理员用户:', adminUser.username);
    
    // 重置密码为 test123
    adminUser.password = 'test123';
    adminUser.isAdmin = true;
    adminUser.isActive = true;
    
    await adminUser.save();
    
    console.log('✅ 管理员密码已重置为: test123');
    console.log('✅ 管理员权限已确认');
    
    // 验证密码
    const isValid = await adminUser.comparePassword('test123');
    console.log('✅ 密码验证结果:', isValid);
    
  } catch (error) {
    console.error('重置管理员密码失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await resetAdminPassword();
  
  console.log('操作完成');
  process.exit(0);
};

// 运行
main().catch(error => {
  console.error('操作失败:', error);
  process.exit(1);
});
