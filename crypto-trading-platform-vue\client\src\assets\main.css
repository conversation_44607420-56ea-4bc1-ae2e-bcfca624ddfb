/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Arial', sans-serif;
}

body {
  background-color: #f5f5f5;
  color: #333;
}

/* 顶部导航栏 */
.header {
  background-color: #0a6e6a;
  color: white;
  padding: 12px 20px;
  border-radius: 0 0 12px 12px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container h1 {
  font-size: 1.3rem;
  margin-bottom: 3px;
}

.logo-container p {
  font-size: 0.8rem;
  opacity: 0.9;
}

.login-button {
  padding: 6px 10px;
  cursor: pointer;
  font-size: 0.8rem;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 卡片样式 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  cursor: pointer;
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-3px);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 1.2rem;
  color: white;
}

.card-title {
  font-size: 0.9rem;
  color: #333;
}

/* 图标颜色 */
.icon-blue {
  background-color: #4a7aff;
}

.icon-purple {
  background-color: #9c6ade;
}

.icon-green {
  background-color: #5cd9a6;
}

.icon-orange {
  background-color: #ff9f43;
}

.icon-yellow {
  background-color: #feca57;
}

.icon-brown {
  background-color: #a67c52;
}

/* 排行榜样式 */
.ranking-section {
  margin-top: 20px;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.ranking-title {
  font-size: 1rem;
  font-weight: bold;
}

.more-link {
  color: #666;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
}

.ranking-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.ranking-card {
  background-color: white;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
}

.ranking-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin: 0 auto 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.ranking-name {
  font-size: 0.8rem;
  margin-bottom: 5px;
}

.ranking-value {
  color: #ff5252;
  font-size: 1.1rem;
  font-weight: bold;
}

.ranking-total {
  font-size: 0.7rem;
  color: #999;
}

/* 市场行情页面 */
.market-header {
  background-color: #0a6e6a;
  color: white;
  padding: 15px 20px;
  border-radius: 0 0 15px 15px;
}

.search-container {
  position: relative;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border-radius: 25px;
  border: none;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  font-size: 0.9rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.market-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.market-table th {
  text-align: left;
  padding: 10px;
  font-size: 0.9rem;
  color: #666;
  border-bottom: 1px solid #eee;
}

.market-table td {
  padding: 15px 10px;
  border-bottom: 1px solid #eee;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0a6e6a;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 用户中心页面 */
.user-header {
  background-color: #0a6e6a;
  color: white;
  padding: 20px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 15px;
  top: 20px;
  font-size: 1.2rem;
}

.menu-button {
  position: absolute;
  right: 15px;
  top: 20px;
  font-size: 1.2rem;
}

.user-profile {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f0f0;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 1.5rem;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.user-id {
  font-size: 0.8rem;
  opacity: 0.8;
}

.user-actions {
  display: flex;
  margin-top: 15px;
}

.user-action {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.8rem;
}

.user-action i {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.upgrade-button {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #333;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
  margin-left: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.upgrade-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.menu-list {
  margin-top: 20px;
}

.menu-item {
  background-color: white;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 15px;
}

.menu-title {
  font-size: 0.9rem;
}

.menu-arrow {
  color: #ccc;
}
