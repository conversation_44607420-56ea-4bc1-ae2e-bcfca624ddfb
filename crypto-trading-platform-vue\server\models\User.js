const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const UserSchema = new mongoose.Schema({
  uid: {
    type: String,
    required: true,
    unique: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  username: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  avatar: {
    type: String,
    default: ''
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  // 会员相关字段
  membershipLevel: {
    type: String,
    enum: ['none', 'monthly', 'quarterly', 'yearly', 'lifetime'],
    default: 'none'
  },
  membershipExpiry: {
    type: Date,
    default: null
  },
  membershipStartDate: {
    type: Date,
    default: null
  },
  lastLoginAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 生成唯一UID
UserSchema.statics.generateUID = function() {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substr(2, 5);
  return `${timestamp}${randomStr}`.toUpperCase();
};

// 密码加密中间件
UserSchema.pre('save', async function(next) {
  // 只有密码被修改时才加密
  if (!this.isModified('password')) return next();

  try {
    // 生成盐值并加密密码
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 更新时间中间件
UserSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 密码验证方法
UserSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// 更新最后登录时间
UserSchema.methods.updateLastLogin = function() {
  this.lastLoginAt = new Date();
  return this.save();
};

// 检查会员是否有效
UserSchema.methods.isValidMember = function() {
  if (this.membershipLevel === 'none') return false;
  if (this.membershipLevel === 'lifetime') return true;
  return this.membershipExpiry && this.membershipExpiry > new Date();
};

// 获取会员剩余天数
UserSchema.methods.getMembershipDaysLeft = function() {
  if (this.membershipLevel === 'none') return 0;
  if (this.membershipLevel === 'lifetime') return -1; // -1 表示永久
  if (!this.membershipExpiry) return 0;

  const now = new Date();
  const expiry = new Date(this.membershipExpiry);

  // 设置时间为当天的开始，避免时间差异
  now.setHours(0, 0, 0, 0);
  expiry.setHours(0, 0, 0, 0);

  const diffTime = expiry - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // 如果已过期，返回0；否则返回剩余天数
  return Math.max(0, diffDays);
};

// 获取用户公开信息
UserSchema.methods.toPublicJSON = function() {
  return {
    uid: this.uid,
    email: this.email,
    username: this.username,
    avatar: this.avatar,
    isActive: this.isActive,
    isAdmin: this.isAdmin,
    membershipLevel: this.membershipLevel,
    membershipExpiry: this.membershipExpiry,
    membershipStartDate: this.membershipStartDate,
    isValidMember: this.isValidMember(),
    membershipDaysLeft: this.getMembershipDaysLeft(),
    lastLoginAt: this.lastLoginAt,
    createdAt: this.createdAt
  };
};

// 索引（email和uid已通过unique: true自动创建索引，无需重复创建）
UserSchema.index({ createdAt: -1 });

module.exports = mongoose.model('User', UserSchema);
